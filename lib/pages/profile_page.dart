import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:go_router/go_router.dart';
import 'package:ailacarte/widgets/themed_background.dart';
import 'package:ailacarte/services/auth_service.dart';
import 'package:ailacarte/theme/custom_theme.dart';
import 'package:ailacarte/di/service_locator.dart';

import 'package:supabase_flutter/supabase_flutter.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;
  User? _user;
  late AuthService _authService;

  @override
  void initState() {
    super.initState();
    _authService = getIt<AuthService>();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      _user = _authService.currentUser;

      if (_user != null && mounted) {
        // Get name from user metadata
        if (_user!.userMetadata != null && _user!.userMetadata!['name'] != null) {
          _nameController.text = _user!.userMetadata!['name'];
        }
        // If no name in metadata, use email prefix
        else if (_user!.email != null && _user!.email!.contains('@')) {
          _nameController.text = _user!.email!.split('@')[0];
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load profile: ${e.toString()}';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate() || !mounted) return;

    final successMessage = 'Profile updated successfully';
    final messenger = ScaffoldMessenger.of(context);

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      if (_user != null) {
        // Update user with new display name
        await Supabase.instance.client.auth.updateUser(
          UserAttributes(
            data: {
              'name': _nameController.text.trim(),
            },
          ),
        );

        // Refresh the user to get updated metadata if still mounted
        if (mounted) {
          _user = _authService.currentUser;

          // Show success message in a post-frame callback
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              messenger.showSnackBar(
                SnackBar(content: Text(successMessage)),
              );
            }
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    if (_isLoading && _user == null) {
      return ThemedBackground(
        child: Scaffold(
          backgroundColor: Colors.transparent,
          appBar: AppBar(
            backgroundColor: ThemedBackground.getAppBarColor(context, isScrolled: true),
            elevation: 0,
            title: Text(
              'Profile',
              style: textTheme.titleLarge,
            ),
            leading: IconButton(
              icon: const Icon(CupertinoIcons.back),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),
          body: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    return ThemedBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          backgroundColor: ThemedBackground.getAppBarColor(context, isScrolled: true),
          elevation: 0,
          title: Text(
            'Profile',
            style: textTheme.titleLarge,
          ),
          leading: IconButton(
            icon: const Icon(CupertinoIcons.back),
            onPressed: () => Navigator.of(context).pop(),
          ),
          actions: [
            TextButton(
              onPressed: _isLoading ? null : _updateProfile,
              child: Text(
                'Save',
                style: TextStyle(
                  color: _isLoading ? colorScheme.secondary.withAlpha(128) : colorScheme.secondary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        body: SafeArea(
          child: Center(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 600),
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                    if (_errorMessage != null)
                      Container(
                        padding: const EdgeInsets.all(12),
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Colors.red.withAlpha(25),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          _errorMessage!,
                          style: const TextStyle(color: Colors.red),
                          textAlign: TextAlign.center,
                        ),
                      ),

                    // Profile header with avatar and name
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
                      decoration: BoxDecoration(
                        color: colorScheme.primary.withAlpha(15),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Column(
                        children: [
                          // Profile picture
                          CircleAvatar(
                            radius: 60,
                            backgroundColor: colorScheme.primary.withAlpha(40),
                            child: Text(
                              _getInitials(),
                              style: TextStyle(
                                fontSize: 42,
                                fontWeight: FontWeight.bold,
                                color: colorScheme.primary,
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          // User email
                          Text(
                            _user?.email ?? '',
                            style: textTheme.bodyLarge?.copyWith(
                              color: CustomTheme.getSecondaryTextColor(context),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Account Information Section Header
                    Padding(
                      padding: const EdgeInsets.only(top: 24, bottom: 0),
                      child: Text(
                        'Account Information',
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: CustomTheme.getPrimaryTextColor(context),
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Name field with card styling
                    Container(
                      decoration: BoxDecoration(
                        color: theme.cardColor,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(20),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(left: 16, top: 16, right: 16),
                            child: Text(
                              'Name',
                              style: textTheme.titleSmall?.copyWith(
                                color: CustomTheme.getSecondaryTextColor(context),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: TextFormField(
                              controller: _nameController,
                              decoration: InputDecoration(
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: const Icon(Icons.person_outline),
                                hintText: 'Enter your name',
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter your name';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Account Actions Section Header
                    Padding(
                      padding: const EdgeInsets.only(top: 16, bottom: 16),
                      child: Text(
                        'Account Actions',
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: CustomTheme.getPrimaryTextColor(context),
                        ),
                      ),
                    ),

                    // Sign out button
                    Container(
                      decoration: BoxDecoration(
                        color: theme.cardColor,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(20),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      margin: const EdgeInsets.only(bottom: 16),
                      child: ListTile(
                        leading: Icon(
                          Icons.logout,
                          color: CustomTheme.getPrimaryIconColor(context),
                        ),
                        title: const Text('Sign Out'),
                        trailing: Icon(
                          Icons.arrow_forward_ios,
                          size: 16,
                          color: CustomTheme.getSecondaryIconColor(context),
                        ),
                        onTap: _isLoading ? null : () => _signOut(context),
                      ),
                    ),

                    // Subtle delete account option
                    Align(
                      alignment: Alignment.center,
                      child: Padding(
                        padding: const EdgeInsets.only(top: 32),
                        child: TextButton(
                          onPressed: _isLoading ? null : () => _deleteAccount(context),
                          child: Text(
                            'Delete Account',
                            style: TextStyle(
                              color: Colors.red.withAlpha(180),
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ),
                    ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  String _getInitials() {
    if (_nameController.text.isEmpty) {
      return _user?.email?.substring(0, 1).toUpperCase() ?? '';
    }

    final nameParts = _nameController.text.split(' ');
    if (nameParts.length > 1) {
      return '${nameParts[0][0]}${nameParts[1][0]}'.toUpperCase();
    }

    return nameParts[0].substring(0, 1).toUpperCase();
  }

  // Simple sign out method that shows a confirmation dialog
  void _signOut(BuildContext context) async {
    final messenger = ScaffoldMessenger.of(context);
    final message = 'Signed out successfully';

    // Show confirmation dialog
    final confirmed = await _showSignOutConfirmationDialog(context);
    
    if (confirmed == true) {
      // Show loading indicator
      if (mounted) {
        setState(() {
          _isLoading = true;
        });
      }

      try { 
        // Sign out
        await _authService.signOut();
       
        if (mounted) {
          // Go to gatekeeper
          context.go('/gatekeeper');

          // Show success message
          messenger.showSnackBar(
            SnackBar(content: Text(message)),
          );
        }
      } catch (e) {
        final errorMsg = 'Error signing out: $e';
        debugPrint(errorMsg);
        
        if (mounted) {
          // Show error message
          messenger.showSnackBar(
            SnackBar(content: Text(errorMsg)),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  // Delete account method with confirmation dialog
  void _deleteAccount(BuildContext context) async {
    final messenger = ScaffoldMessenger.of(context);
    final message = 'Account deleted successfully';

    // Show confirmation dialog
    final confirmed = await _showDeleteAccountConfirmationDialog(context);
    
    if (confirmed == true) {
      if (mounted) {
        setState(() {
          _isLoading = true;
        });
      }

      try {
        // Delete account
        await _authService.deleteAccount();
       
        if (mounted) {
          // Go to gatekeeper
          context.go('/gatekeeper');

          // Show success message
          messenger.showSnackBar(
            SnackBar(content: Text(message)),
          );
        }
      } catch (e) {
        final errorMsg = 'Error deleting account: $e';
        debugPrint(errorMsg);
        
        if (mounted) {
          // Show error message
          messenger.showSnackBar(
            SnackBar(content: Text(errorMsg)),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  Future<bool?> _showSignOutConfirmationDialog(BuildContext context) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }

  Future<bool?> _showDeleteAccountConfirmationDialog(BuildContext context) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account'),
        content: const Text('Are you sure you want to delete your account? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
