import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:ailacarte/utils/app_links.dart';
import 'package:ailacarte/theme/custom_theme.dart';
import 'package:ailacarte/widgets/themed_background.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class AboutScreen extends StatefulWidget {
  const AboutScreen({super.key});

  @override
  State<AboutScreen> createState() => _AboutScreenState();
}

class _AboutScreenState extends State<AboutScreen> {
  String _version = '';
  String _buildNumber = '';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPackageInfo();
  }

  Future<void> _loadPackageInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      if (mounted) {
        setState(() {
          _version = packageInfo.version;
          _buildNumber = packageInfo.buildNumber;
          _isLoading = false;
        });
      }
    } catch (e) {
      // Handle MissingPluginException or any other errors
      if (mounted) {
        setState(() {
          _version = '1.0.0'; // Default version
          _buildNumber = '1';  // Default build number
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _launchURL(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        final errorMessage = 'Could not launch $url';
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
          ),
        );
      }
    }
  }

  Future<void> _shareApp() async {
    try {
      final String shareMessage = 'Check out AI La Carte - Your AI-powered recipe collection app! ${AppLinks.appUrl}';
      final String shareSubject = 'AI La Carte App';

      await Share.share(
        shareMessage,
        subject: shareSubject,
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not share app')),
        );
      }
    }
  }

  Future<void> _sendFeedback() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: AppLinks.feedbackEmail,
      query: 'subject=${Uri.encodeComponent('AI La Carte Feedback')}&body=${Uri.encodeComponent('Hi there,\n\nI have some feedback about AI La Carte:\n\n')}',
    );

    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not launch email app'),
          ),
        );
      }
    }
  }

  Widget _buildSocialMediaLinks() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildSocialButton(
          icon: FontAwesomeIcons.xTwitter,
          onTap: () => _launchURL(AppLinks.twitter),
          tooltip: 'Twitter',
        ),
        _buildSocialButton(
          icon: FontAwesomeIcons.instagram,
          onTap: () => _launchURL(AppLinks.instagram),
          tooltip: 'Instagram',
        ),
      ],
    );
  }

  Widget _buildSocialButton({
    required IconData icon,
    required VoidCallback onTap,
    required String tooltip,
  }) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Tooltip(
        message: tooltip,
        child: Material(
          color: isDarkMode
              ? theme.cardColor.withAlpha(150)
              : theme.colorScheme.primary.withAlpha(15),
          borderRadius: BorderRadius.circular(30),
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(30),
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: FaIcon(
                icon,
                size: 20,
                color: theme.colorScheme.primary,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: CustomTheme.getShadowColor(context, opacity: 0.05),
              blurRadius: 8,
              spreadRadius: 0,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
            child: Container(
              decoration: BoxDecoration(
                color: theme.cardColor.withAlpha(100),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isDarkMode
                      ? Colors.white.withAlpha(20)
                      : Colors.black.withAlpha(10),
                  width: 0.5,
                ),
              ),
              child: ListTile(
                leading: Icon(icon, color: theme.colorScheme.primary.withAlpha(220)),
                title: Text(title),
                subtitle: subtitle != null ? Text(subtitle) : null,
                trailing: const Icon(Icons.chevron_right),
                onTap: onTap,
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;

    return ThemedBackground(
      showGradient: false,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
        backgroundColor: ThemedBackground.getAppBarColor(context),
        title: Text(
          'About',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        automaticallyImplyLeading: true,
        leading: IconButton(
          icon: const Icon(CupertinoIcons.back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: _buildBody(theme, localizations),
    ),
    );
  }

  Widget _buildBody(ThemeData theme, AppLocalizations localizations) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 600),
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // App logo
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withAlpha(20),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Icon(
                  Icons.restaurant_menu,
                  size: 60,
                  color: theme.colorScheme.primary,
                ),
              ),

              // App name and version
              const SizedBox(height: 16),
              Text(
                'AI La Carte',
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Version $_version ($_buildNumber)',
                style: theme.textTheme.bodyMedium,
              ),
              const SizedBox(height: 8),
              Text(
                'Your AI-powered recipe collection app',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 24),

              // Social media links
              _buildSocialMediaLinks(),

              const SizedBox(height: 32),

              // Action items
              _buildSettingItem(
                icon: Icons.privacy_tip_outlined,
                title: 'Privacy Policy',
                onTap: () => _launchURL(AppLinks.privacyPolicy),
              ),
              _buildSettingItem(
                icon: Icons.feedback_outlined,
                title: 'Send Feedback',
                onTap: _sendFeedback,
              ),
              _buildSettingItem(
                icon: Icons.star_outline,
                title: 'Rate App',
                onTap: () => _launchURL(AppLinks.appStoreUrl),
              ),
              _buildSettingItem(
                icon: Icons.share_outlined,
                title: 'Share App',
                onTap: _shareApp,
              ),
              _buildSettingItem(
                icon: Icons.description_outlined,
                title: 'Terms of Service',
                onTap: () => _launchURL(AppLinks.termsOfService),
              ),
              _buildSettingItem(
                icon: Icons.help_outline,
                title: 'Help & FAQ',
                onTap: () => _launchURL(AppLinks.helpUrl),
              ),

              const SizedBox(height: 24),

              // Copyright
              Text(
                '© 2025 AI La Carte',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.primary.withAlpha(180),
                ),
              ),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }
}
