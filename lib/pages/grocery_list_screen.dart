import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:ailacarte/widgets/themed_background.dart';
import 'package:ailacarte/database/database.dart';
import 'package:ailacarte/di/service_locator.dart';
import 'package:flutter_gen/gen_l10n/l10n.dart';
import 'package:ailacarte/widgets/grocery_item_dialog.dart';
import 'package:ailacarte/blocs/grocery_bloc/grocery_bloc.dart';
import 'package:ailacarte/blocs/grocery_bloc/grocery_event.dart';
import 'package:ailacarte/blocs/grocery_bloc/grocery_state.dart';

class GroceryListScreen extends StatelessWidget {
  const GroceryListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<GroceryBloc>()..add(LoadGroceryItems()),
      child: const _GroceryListView(),
    );
  }
}

class _GroceryListView extends StatefulWidget {
  const _GroceryListView();

  @override
  State<_GroceryListView> createState() => _GroceryListViewState();
}

class _GroceryListViewState extends State<_GroceryListView> {
  final ScrollController _scrollController = ScrollController();
  bool _showTitle = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.hasClients) {
      final showTitle = _scrollController.offset > 30;
      if (showTitle != _showTitle) {
        setState(() {
          _showTitle = showTitle;
        });
      }
    }
  }

  void _showAddGroceryItemDialog({String? recipeId}) {
    showDialog(
      context: context,
      builder: (context) => GroceryItemDialog(
        onSave: (name, quantity, category, notes, recipeId) {
          context.read<GroceryBloc>().add(AddGroceryItem(
            name: name,
            quantity: quantity,
            category: category,
            notes: notes,
            recipeId: recipeId,
          ));
          Navigator.of(context).pop();
        },
        recipeId: recipeId,
      ),
    );
  }

  void _showEditGroceryItemDialog(GroceryItem item) {
    showDialog(
      context: context,
      builder: (context) => GroceryItemDialog(
        item: item,
        onSave: (name, quantity, category, notes, _) {
          context.read<GroceryBloc>().add(UpdateGroceryItem(
            id: item.id,
            name: name,
            quantity: quantity,
            category: category,
            notes: notes,
          ));
          Navigator.of(context).pop();
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return ThemedBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: BlocConsumer<GroceryBloc, GroceryState>(
          listener: (context, state) {
            if (state.status == GroceryStatus.failure) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(state.errorMessage ?? 'An error occurred')),
              );
            }
          },
          builder: (context, state) {
            return Stack(
              children: [
                CustomScrollView(
                  controller: _scrollController,
                  slivers: [
                    SliverAppBar(
                      expandedHeight: 80,
                      floating: false,
                      pinned: true,
                      backgroundColor: ThemedBackground.getAppBarColor(context, isScrolled: _showTitle),
                      title: _showTitle
                          ? Text(
                              l10n.groceryList,
                              style: theme.textTheme.titleLarge,
                            )
                          : null,
                      flexibleSpace: FlexibleSpaceBar(
                        background: Container(
                          padding: const EdgeInsets.fromLTRB(24, 60, 24, 16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Text(
                                l10n.groceryList,
                                style: theme.textTheme.headlineLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.primary,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    // Filter chips
                    SliverToBoxAdapter(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: [
                              for (int i = 0; i < state.availableFilters.length; i++) ...[
                                _buildFilterChip(
                                  state.availableFilters[i] == 'All' ? l10n.all :
                                  state.availableFilters[i] == 'Other' ? l10n.categoryOther :
                                  state.availableFilters[i],
                                  state.availableFilters[i],
                                  state.selectedFilter,
                                ),
                                if (i < state.availableFilters.length - 1) const SizedBox(width: 8),
                              ],
                            ],
                          ),
                        ),
                      ),
                    ),

                    // Grocery list
                    if (state.status == GroceryStatus.loading)
                      const SliverFillRemaining(
                        child: Center(child: CircularProgressIndicator()),
                      )
                    else if (state.filteredItems.isEmpty)
                      SliverFillRemaining(
                        child: _buildEmptyState(context),
                      )
                    else
                      SliverPadding(
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        sliver: SliverList(
                          delegate: SliverChildBuilderDelegate(
                            (context, index) {
                              final item = state.filteredItems[index];
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 8),
                                child: _buildGroceryItem(context, item),
                              );
                            },
                            childCount: state.filteredItems.length,
                          ),
                        ),
                      ),
                  ],
                ),

                // Floating Action Button
                Positioned(
                  bottom: 100,
                  right: 32,
                  child: FloatingActionButton(
                    onPressed: () => _showAddGroceryItemDialog(),
                    backgroundColor: theme.colorScheme.primary,
                    shape: const CircleBorder(),
                    child: const Icon(Icons.add, color: Colors.white),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, String value, String selectedFilter) {
    final theme = Theme.of(context);
    final isSelected = selectedFilter == value;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        context.read<GroceryBloc>().add(FilterGroceryItems(value));
      },
      backgroundColor: theme.colorScheme.surface.withAlpha(100),
      selectedColor: theme.colorScheme.primary.withAlpha(50),
      checkmarkColor: theme.colorScheme.primary,
      labelStyle: TextStyle(
        color: isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurface,
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
      ),
      side: BorderSide(
        color: isSelected ? theme.colorScheme.primary : theme.colorScheme.outline.withAlpha(100),
        width: isSelected ? 2 : 1,
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha(20),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.shopping_cart_outlined,
              size: 60,
              color: theme.colorScheme.primary.withAlpha(100),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            l10n.noItemsYet,
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(150),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            l10n.addItemsToStartBuildingYourGroceryList,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(120),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildGroceryItem(BuildContext context, GroceryItem item) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      child: Row(
        children: [
          // Checkbox
          SizedBox(
            width: 40,
            child: Checkbox(
              value: item.isCompleted,
              onChanged: (value) {
                context.read<GroceryBloc>().add(ToggleGroceryItem(item.id));
              },
              activeColor: theme.colorScheme.primary,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ),

          // Item details
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.name,
                    style: TextStyle(
                      decoration: item.isCompleted ? TextDecoration.lineThrough : null,
                      color: item.isCompleted
                          ? theme.colorScheme.onSurface.withAlpha(120)
                          : theme.colorScheme.onSurface,
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                    ),
                  ),
                  if (item.quantity.isNotEmpty) ...[
                    const SizedBox(height: 2),
                    Text(
                      item.quantity,
                      style: TextStyle(
                        color: theme.colorScheme.onSurface.withAlpha(150),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),

          // Action buttons
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: Icon(
                  Icons.edit_outlined,
                  color: theme.colorScheme.primary.withAlpha(150),
                  size: 18,
                ),
                onPressed: () => _showEditGroceryItemDialog(item),
                padding: const EdgeInsets.all(8),
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
              ),
              IconButton(
                icon: Icon(
                  Icons.delete_outline,
                  color: theme.colorScheme.error.withAlpha(150),
                  size: 18,
                ),
                onPressed: () {
                  context.read<GroceryBloc>().add(DeleteGroceryItem(item.id));
                },
                padding: const EdgeInsets.all(8),
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
