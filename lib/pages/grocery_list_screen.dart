
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:ailacarte/theme/custom_theme.dart';
import 'package:ailacarte/widgets/themed_background.dart';
import 'package:ailacarte/database/database.dart';
import 'package:drift/drift.dart' hide Column;

import 'package:ailacarte/di/service_locator.dart';
import 'package:flutter_gen/gen_l10n/l10n.dart';
import 'package:uuid/uuid.dart';

import 'package:ailacarte/widgets/grocery_item_dialog.dart';

class GroceryListScreen extends StatefulWidget {
  const GroceryListScreen({super.key});

  @override
  State<GroceryListScreen> createState() => _GroceryListScreenState();
}

class _GroceryListScreenState extends State<GroceryListScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _textController = TextEditingController();
  final AppDatabase _database = getIt<AppDatabase>();
  final Uuid _uuid = const Uuid();

  bool _showTitle = false;
  List<GroceryItem> _groceryItems = [];
  List<GroceryItem> _filteredItems = [];
  String _selectedFilter = 'All';

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadGroceryItems();
  }

  Future<void> _loadGroceryItems() async {
    try {
      final items = await _database.getAllGroceryItems();
      setState(() {
        _groceryItems = items;
        _applyFilter();
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading grocery items: $e')),
        );
      }
    }
  }

  void _applyFilter() {
    if (_selectedFilter == 'All') {
      _filteredItems = _groceryItems;
    } else {
      _filteredItems = _groceryItems.where((item) {
        if (_selectedFilter == 'Other') {
          return item.recipeId == null;
        } else {
          // Filter by recipe name (we'll need to get recipe names)
          return item.recipeId != null;
        }
      }).toList();
    }
  }



  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _textController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.hasClients) {
      final showTitle = _scrollController.offset > 30;
      if (showTitle != _showTitle) {
        setState(() {
          _showTitle = showTitle;
        });
      }
    }
  }

  Future<void> _toggleItem(String id) async {
    try {
      await _database.toggleGroceryItemCompletion(id);
      _loadGroceryItems(); // Refresh the list
    } catch (e) {
      if (mounted) {
        final l10n = AppLocalizations.of(context)!;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${l10n.errorUpdatingItem}: $e')),
        );
      }
    }
  }

  Future<void> _addItem(String name, String quantity, String category, String? notes, String? recipeId) async {
    try {
      final now = DateTime.now();
      final newItem = GroceryItemsCompanion.insert(
        id: _uuid.v4(),
        name: name,
        quantity: quantity,
        category: category,
        notes: Value(notes),
        recipeId: Value(recipeId), // Map to recipe if provided
        createdAt: now,
      );

      await _database.insertGroceryItem(newItem);
      _loadGroceryItems(); // Refresh the list
    } catch (e) {
      if (mounted) {
        final l10n = AppLocalizations.of(context)!;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${l10n.errorAddingItem}: $e')),
        );
      }
    }
  }

  Future<void> _removeItem(String id) async {
    try {
      await _database.deleteGroceryItem(id);
      _loadGroceryItems(); // Refresh the list
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting item: $e')),
        );
      }
    }
  }



  Future<void> _showAddGroceryItemDialog() async {
    await showCupertinoDialog(
      context: context,
      builder: (context) {
        return GroceryItemDialog(
          onSave: (name, quantity, category, notes, recipeId) async {
            await _addItem(name, quantity, category, notes, recipeId);
          },
        );
      },
    );
  }

  Future<void> _showEditGroceryItemDialog(GroceryItem item) async {
    await showCupertinoDialog(
      context: context,
      builder: (context) {
        return GroceryItemDialog(
          item: item,
          onSave: (name, quantity, category, notes, recipeId) async {
            await _updateItem(item.id, name, quantity, category, notes);
          },
        );
      },
    );
  }

  Future<void> _updateItem(String id, String name, String quantity, String category, String? notes) async {
    try {
      final updatedItem = GroceryItemsCompanion(
        id: Value(id),
        name: Value(name),
        quantity: Value(quantity),
        category: Value(category),
        notes: Value(notes),
      );

      await _database.updateGroceryItem(updatedItem);
      _loadGroceryItems(); // Refresh the list
    } catch (e) {
      if (mounted) {
        final l10n = AppLocalizations.of(context)!;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${l10n.errorUpdatingItem}: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return ThemedBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Stack(
          children: [
            CustomScrollView(
              controller: _scrollController,
              slivers: [
                SliverAppBar(
                  expandedHeight: 200,
                  floating: false,
                  pinned: true,
                  backgroundColor: ThemedBackground.getAppBarColor(context, isScrolled: _showTitle),
                  title: _showTitle
                      ? Text(
                          l10n.groceryList,
                          style: theme.textTheme.titleLarge,
                        )
                      : null,
                  flexibleSpace: FlexibleSpaceBar(
                    background: Container(
                      padding: const EdgeInsets.fromLTRB(24, 60, 24, 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            l10n.groceryList,
                            style: theme.textTheme.headlineLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // Filter chips
                SliverToBoxAdapter(
                  child: Container(
                    height: 60,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: ListView(
                      scrollDirection: Axis.horizontal,
                      children: [
                        _buildFilterChip(l10n.all, 'All'),
                        const SizedBox(width: 8),
                        _buildFilterChip(l10n.categoryOther, 'Other'),
                        // Add more filter chips for recipes if needed
                      ],
                    ),
                  ),
                ),

                // Grocery list
                _filteredItems.isEmpty
                    ? SliverFillRemaining(
                        child: _buildEmptyState(context),
                      )
                    : SliverPadding(
                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 100),
                        sliver: SliverList(
                          delegate: SliverChildBuilderDelegate(
                            (context, index) {
                              final item = _filteredItems[index];
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 8),
                                child: _buildGroceryItem(context, item),
                              );
                            },
                            childCount: _filteredItems.length,
                          ),
                        ),
                      ),
              ],
            ),

            // Floating Action Button
            Positioned(
              bottom: 32,
              right: 32,
              child: FloatingActionButton(
                onPressed: _showAddGroceryItemDialog,
                backgroundColor: theme.colorScheme.primary,
                shape: const CircleBorder(),
                child: const Icon(Icons.add, color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    final theme = Theme.of(context);
    final isSelected = _selectedFilter == value;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedFilter = value;
          _applyFilter();
        });
      },
      backgroundColor: theme.cardColor.withAlpha(100),
      selectedColor: theme.colorScheme.primary.withAlpha(50),
      checkmarkColor: theme.colorScheme.primary,
      labelStyle: TextStyle(
        color: isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurface,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
      side: BorderSide(
        color: isSelected ? theme.colorScheme.primary : theme.colorScheme.outline.withAlpha(50),
        width: isSelected ? 2 : 1,
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha((0.1 * 255).round()),
              borderRadius: BorderRadius.circular(60),
            ),
            child: Icon(
              Icons.shopping_cart,
              size: 60,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            l10n.noItemsYet,
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            l10n.addItemsToGroceryList,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.textTheme.bodyLarge?.color?.withAlpha((0.7 * 255).round()),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildGroceryItem(BuildContext context, GroceryItem item) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: theme.cardColor.withAlpha(50),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.outline.withAlpha(30),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Checkbox
          Checkbox(
            value: item.isCompleted,
            onChanged: (value) => _toggleItem(item.id),
            activeColor: theme.colorScheme.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4),
            ),
          ),

          const SizedBox(width: 12),

          // Item details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.name,
                  style: TextStyle(
                    decoration: item.isCompleted ? TextDecoration.lineThrough : null,
                    color: item.isCompleted
                        ? theme.textTheme.bodyLarge?.color?.withAlpha(128)
                        : theme.textTheme.bodyLarge?.color,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      item.quantity,
                      style: TextStyle(
                        color: item.isCompleted
                            ? theme.textTheme.bodySmall?.color?.withAlpha(128)
                            : theme.colorScheme.primary,
                        fontWeight: FontWeight.w500,
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: _getCategoryColor(item.category).withAlpha(25),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        item.category,
                        style: TextStyle(
                          fontSize: 10,
                          color: _getCategoryColor(item.category),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Action buttons
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: () => _showEditGroceryItemDialog(item),
                icon: const Icon(Icons.edit_outlined),
                color: theme.colorScheme.onSurface.withAlpha(150),
                iconSize: 20,
              ),
              IconButton(
                onPressed: () => _removeItem(item.id),
                icon: const Icon(Icons.delete_outline),
                color: Colors.red.withAlpha(180),
                iconSize: 20,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'vegetables':
        return CustomTheme.recipeGreen;
      case 'meat':
        return CustomTheme.recipeRed;
      case 'dairy':
        return Colors.blue;
      case 'pantry':
        return CustomTheme.recipeYellow;
      case 'bakery':
        return CustomTheme.recipeOrange;
      default:
        return CustomTheme.primaryColor;
    }
  }
}
