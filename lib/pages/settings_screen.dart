import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:ailacarte/theme/custom_theme.dart';
import 'package:ailacarte/widgets/themed_background.dart';
import 'package:ailacarte/widgets/setting_item.dart';
import 'package:ailacarte/widgets/translucent_dialog.dart';
import 'package:ailacarte/blocs/theme_bloc/theme_bloc.dart';
import 'package:ailacarte/blocs/language_bloc/language_bloc.dart';
import 'package:ailacarte/services/auth_service.dart';

import 'package:ailacarte/di/service_locator.dart';
import 'package:ailacarte/l10n/l10n.dart';
import 'package:ailacarte/pages/profile_page.dart';
import 'package:ailacarte/pages/notification_settings_screen.dart';
import 'package:ailacarte/data/settings_preference.dart';
import 'package:ailacarte/utils/app_links.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';


class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _showTitle = false;
  String _appVersion = '';

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadAppInfo();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.hasClients) {
      final showTitle = _scrollController.offset > 30;
      if (showTitle != _showTitle) {
        setState(() {
          _showTitle = showTitle;
        });
      }
    }
  }

  Future<void> _loadAppInfo() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      _appVersion = '${packageInfo.version} (${packageInfo.buildNumber})';
    });
  }

  @override
  Widget build(BuildContext context) {
    final authService = getIt<AuthService>();
    final currentUser = authService.currentUser;

    return _buildSettingsContent(context, currentUser);
  }

  Widget _buildSettingsContent(BuildContext context, dynamic currentUser) {
    return ThemedBackground(
      showGradient: false,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: CustomScrollView(
          controller: _scrollController,
          physics: const AlwaysScrollableScrollPhysics(),
          slivers: [
            SliverAppBar(
              pinned: true,
              floating: false,
              automaticallyImplyLeading: false,
              backgroundColor: ThemedBackground.getAppBarColor(context),
              expandedHeight: 64,
              title: _showTitle ? Text(
                'Settings',
                style: Theme.of(context).textTheme.titleLarge,
              ) : null,
              titleSpacing: 0,
              flexibleSpace: FlexibleSpaceBar(
                expandedTitleScale: 1.0,
                titlePadding: EdgeInsets.zero,
                background: Padding(
                  padding: EdgeInsets.only(
                    top: MediaQuery.of(context).padding.top,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.only(
                      top: 16,
                      left: 16,
                      right: 16,
                      bottom: 0,
                    ),
                    child: Text(
                      'Settings',
                      style: Theme.of(context).textTheme.headlineLarge,
                    ),
                  ),
                ),
              ),
            ),
            SliverList(
              delegate: SliverChildListDelegate([
                // Account section with future builder to handle async user data
                Center(
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(maxWidth: 600),
                    child: FutureBuilder<Widget>(
                      future: _buildAccountSection(context, currentUser),
                      builder: (context, snapshot) {
                        if (snapshot.connectionState == ConnectionState.waiting) {
                          return const Center(child: CircularProgressIndicator());
                        }
                        return snapshot.data ?? Container();
                      },
                    ),
                  ),
                ),
                // Preferences section
                Center(
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(maxWidth: 600),
                    child: _buildPreferencesSection(context),
                  ),
                ),
                const SizedBox(height: 16),
                // Notifications section
                Center(
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(maxWidth: 600),
                    child: _buildNotificationsSection(context),
                  ),
                ),
                const SizedBox(height: 16),
                // About section
                Center(
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(maxWidth: 600),
                    child: _buildAboutSection(context),
                  ),
                ),
                const SizedBox(height: 100),
              ]),
            ),
          ],
        ),
      ),
    );
  }

  Future<Widget> _buildAccountSection(BuildContext context, dynamic currentUser) async {
    final items = <SettingItem>[];

    if (currentUser != null) {
      // Get user name from auth user
      String userName = 'User';

      // Try to get display name from user
      if (currentUser.userMetadata != null && currentUser.userMetadata!['name'] != null) {
        userName = currentUser.userMetadata!['name'];
      }
      // If no name in metadata, try to get from email (before the @ symbol)
      else if (currentUser.email != null && currentUser.email!.contains('@')) {
        userName = currentUser.email!.split('@')[0];
      }

      items.add(
        SettingItem(
          icon: Icons.account_circle,
          title: userName,
          subtitle: currentUser.email,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const ProfilePage()),
            );
          },
        ),
      );
    } else {
      items.add(
        SettingItem(
          icon: Icons.account_circle,
          title: 'Guest User',
          onTap: () => context.go('/login'),
        ),
      );
    }

    return _buildSection(
      context,
      title: 'Account',
      items: items,
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required List<SettingItem> items,
    Color? titleColor,
  }) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title.isNotEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                color: titleColor ?? CustomTheme.getPrimaryTextColor(context),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: CustomTheme.getShadowColor(context, opacity: 0.05),
                  blurRadius: 8,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isDarkMode
                      ? Colors.white.withAlpha(20)
                      : Colors.black.withAlpha(10),
                  width: 0.5,
                ),
              ),
              child: Column(
                children: items.asMap().entries.map((entry) {
                      final item = entry.value;
                      final isLast = entry.key == items.length - 1;
                      return Column(
                        children: [
                          ListTile(
                            leading: Icon(item.icon,
                              color: (titleColor ?? theme.colorScheme.primary).withAlpha(220)),
                            title: Text(
                              item.title,
                              style: TextStyle(
                                color: CustomTheme.getPrimaryTextColor(context).withAlpha(220),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            subtitle: item.subtitle != null
                                ? Text(
                                    item.subtitle!,
                                    style: TextStyle(
                                      color: CustomTheme.getSecondaryTextColor(context).withAlpha(180),
                                      fontSize: 13,
                                    ),
                                  )
                                : null,
                            trailing: item.trailing,
                            onTap: item.onTap,
                          ),
                          if (!isLast)
                            Divider(
                              height: 1,
                              indent: 16,
                              endIndent: 16,
                              color: isDarkMode
                                  ? Colors.white.withAlpha(20)
                                  : Colors.black.withAlpha(10),
                            ),
                        ],
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPreferencesSection(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            'Preferences',
            style: theme.textTheme.titleMedium?.copyWith(
              color: CustomTheme.getPrimaryTextColor(context),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: CustomTheme.getShadowColor(context, opacity: 0.05),
                  blurRadius: 8,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isDarkMode
                      ? Colors.white.withAlpha(20)
                      : Colors.black.withAlpha(10),
                  width: 0.5,
                ),
              ),
              child: Column(
                children: [
                  // Language setting
                  BlocBuilder<LanguageBloc, LanguageState>(
                    builder: (context, state) {
                      return ListTile(
                        leading: Icon(Icons.language_outlined,
                          color: theme.colorScheme.primary.withAlpha(220)),
                        title: Text(
                          AppLocalizations.of(context)!.selectLanguage,
                          style: TextStyle(
                            color: CustomTheme.getPrimaryTextColor(context).withAlpha(220),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        subtitle: Text(
                          L10n.getLanguageName(state.locale.languageCode),
                          style: TextStyle(
                            color: CustomTheme.getSecondaryTextColor(context).withAlpha(180),
                            fontSize: 13,
                          ),
                        ),
                        onTap: () => _showLanguageDialog(context),
                      );
                    },
                  ),
                  Divider(
                    height: 1,
                    indent: 16,
                    endIndent: 16,
                    color: isDarkMode
                        ? Colors.white.withAlpha(20)
                        : Colors.black.withAlpha(10),
                  ),
                  // Theme setting
                  BlocBuilder<ThemeBloc, ThemeState>(
                    builder: (context, state) {
                      return ListTile(
                        leading: Icon(Icons.color_lens_outlined,
                          color: theme.colorScheme.primary.withAlpha(220)),
                        title: Text(
                          'Theme',
                          style: TextStyle(
                            color: CustomTheme.getPrimaryTextColor(context).withAlpha(220),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        subtitle: Text(
                          _getThemeName(state.themeMode),
                          style: TextStyle(
                            color: CustomTheme.getSecondaryTextColor(context).withAlpha(180),
                            fontSize: 13,
                          ),
                        ),
                        onTap: () => _showThemeDialog(context),
                      );
                    },
                  ),
                  Divider(
                    height: 1,
                    indent: 16,
                    endIndent: 16,
                    color: isDarkMode
                        ? Colors.white.withAlpha(20)
                        : Colors.black.withAlpha(10),
                  ),
                  // Premium setting
                  ListTile(
                    leading: Icon(Icons.star,
                      color: theme.colorScheme.primary.withAlpha(220)),
                    title: Text(
                      'Upgrade to Premium',
                      style: TextStyle(
                        color: CustomTheme.getPrimaryTextColor(context).withAlpha(220),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    subtitle: Text(
                      'Unlock all features',
                      style: TextStyle(
                        color: CustomTheme.getSecondaryTextColor(context).withAlpha(180),
                        fontSize: 13,
                      ),
                    ),
                    onTap: () => context.go('/paywall'),
                  ),
                  Divider(
                    height: 1,
                    indent: 16,
                    endIndent: 16,
                    color: isDarkMode
                        ? Colors.white.withAlpha(20)
                        : Colors.black.withAlpha(10),
                  ),

                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNotificationsSection(BuildContext context) {
    return FutureBuilder<bool>(
      future: SettingsPreferences.getNotificationsEnabled(),
      builder: (context, snapshot) {
        final notificationsEnabled = snapshot.data ?? false;

        return _buildSection(
          context,
          title: 'Notifications',
          items: [
            SettingItem(
              icon: Icons.notifications,
              title: 'Notification Settings',
              subtitle: notificationsEnabled ? 'Enabled' : 'Disabled',
              onTap: () async {
                await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const NotificationSettingsScreen(),
                  ),
                );
                // Refresh the UI when returning from notification settings
                if (context.mounted) setState(() {});
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildAboutSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'About',
      items: [
        SettingItem(
          icon: Icons.info_outline,
          title: 'AI La Carte',
          subtitle: 'Version $_appVersion',
          onTap: null,
        ),
        SettingItem(
          icon: Icons.privacy_tip_outlined,
          title: 'Privacy Policy',
          onTap: () => _launchURL(AppLinks.privacyPolicy),
        ),
        SettingItem(
          icon: Icons.description_outlined,
          title: 'Terms of Service',
          onTap: () => _launchURL(AppLinks.termsOfService),
        ),
        SettingItem(
          icon: Icons.feedback_outlined,
          title: 'Send Feedback',
          onTap: _sendFeedback,
        ),
        SettingItem(
          icon: Icons.star_outline,
          title: 'Rate App',
          onTap: () => _launchURL(AppLinks.appStoreUrl),
        ),
        SettingItem(
          icon: Icons.share_outlined,
          title: 'Share App',
          onTap: _shareApp,
        ),
        SettingItem(
          icon: Icons.help_outline,
          title: 'Help & FAQ',
          onTap: () => _launchURL(AppLinks.helpUrl),
        ),
      ],
    );
  }

  Future<void> _launchURL(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not launch $url')),
        );
      }
    }
  }

  Future<void> _shareApp() async {
    try {
      const String shareMessage = 'Check out AI La Carte - Your AI-powered recipe collection app! ${AppLinks.appUrl}';
      const String shareSubject = 'AI La Carte App';

      await Share.share(
        shareMessage,
        subject: shareSubject,
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not share app')),
        );
      }
    }
  }

  Future<void> _sendFeedback() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: AppLinks.feedbackEmail,
      query: 'subject=${Uri.encodeComponent('AI La Carte Feedback')}&body=${Uri.encodeComponent('Hi there,\n\nI have some feedback about AI La Carte:\n\n')}',
    );

    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not launch email app')),
        );
      }
    }
  }

  String _getThemeName(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }

  void _showThemeDialog(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    showDialog(
      context: context,
      builder: (context) {
        return TranslucentDialog(
          title: 'Theme',
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: ThemeMode.values.map((themeMode) {
              return RadioListTile<ThemeMode>(
                title: Text(_getThemeModeText(themeMode)),
                value: themeMode,
                groupValue: context.watch<ThemeBloc>().state.themeMode,
                onChanged: (ThemeMode? newThemeMode) {
                  if (newThemeMode != null) {
                    context.read<ThemeBloc>().add(ChangeThemeEvent(newThemeMode));
                    Navigator.pop(context);
                  }
                },
                activeColor: colorScheme.primary,
              );
            }).toList(),
          ),
        );
      },
    );
  }

  String _getThemeModeText(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.system:
        return 'System';
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
    }
  }

  void _showLanguageDialog(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    showDialog(
      context: context,
      builder: (context) {
        return TranslucentDialog(
          title: AppLocalizations.of(context)!.selectLanguage,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: L10n.supportedLocales.map((locale) {
              return RadioListTile<Locale>(
                title: Text(L10n.getLanguageName(locale.languageCode)),
                value: locale,
                groupValue: context.watch<LanguageBloc>().state.locale,
                onChanged: (value) {
                  if (value != null) {
                    context.read<LanguageBloc>().add(ChangeLanguageEvent(value));
                    Navigator.pop(context);
                  }
                },
                activeColor: colorScheme.primary,
              );
            }).toList(),
          ),
        );
      },
    );
  }
}
