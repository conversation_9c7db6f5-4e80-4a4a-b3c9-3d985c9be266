import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:ailacarte/database/database.dart';
import 'package:ailacarte/services/data_sync_service.dart';
import 'package:ailacarte/widgets/themed_background.dart';
import 'package:ailacarte/di/service_locator.dart';
import 'package:ailacarte/models/recipe_enums.dart';


class RecipeDetailScreen extends StatefulWidget {
  final String recipeId;

  const RecipeDetailScreen({
    super.key,
    required this.recipeId,
  });

  @override
  State<RecipeDetailScreen> createState() => _RecipeDetailScreenState();
}

class _RecipeDetailScreenState extends State<RecipeDetailScreen> {
  final AppDatabase _database = getIt<AppDatabase>();
  Recipe? _recipe;
  List<Ingredient> _ingredients = [];
  List<String> _imageUrls = [];
  bool _isLoading = true;
  bool _isFavorite = false;
  int _currentImageIndex = 0;
  final ScrollController _scrollController = ScrollController();
  bool _isCollapsed = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadRecipeData();
  }

  void _onScroll() {
    // AppBar is considered collapsed if offset > (expandedHeight - kToolbarHeight)
    final double expandedHeight = 300;
    final bool collapsed = _scrollController.hasClients &&
        _scrollController.offset > (expandedHeight - kToolbarHeight - 20);
    if (collapsed != _isCollapsed) {
      setState(() {
        _isCollapsed = collapsed;
      });
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadRecipeData() async {
    try {
      final recipe = await _database.getRecipeById(widget.recipeId);
      final ingredients = await _database.getRecipeIngredients(widget.recipeId);
      
      if (recipe != null) {
        setState(() {
          _recipe = recipe;
          _ingredients = ingredients;
          _isFavorite = recipe.isFavorite;
          _imageUrls = recipe.imageUrls != null 
              ? List<String>.from(jsonDecode(recipe.imageUrls!))
              : [];
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading recipe: $e')),
        );
      }
    }
  }

  Future<void> _toggleFavorite() async {
    if (_recipe != null) {
      final newFavoriteStatus = await _database.toggleRecipeFavorite(_recipe!.id);
      setState(() {
        _isFavorite = newFavoriteStatus;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    if (_isLoading) {
      return Scaffold(
        body: ThemedBackground(
          child: Center(
            child: CircularProgressIndicator(
              color: theme.colorScheme.primary,
            ),
          ),
        ),
      );
    }

    if (_recipe == null) {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
        ),
        body: ThemedBackground(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: theme.colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  l10n.recipeNotFound,
                  style: theme.textTheme.headlineSmall,
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Scaffold(
      body: ThemedBackground(
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            // Image carousel with recipe title overlay
            SliverAppBar(
              expandedHeight: 300,
              pinned: true,
              backgroundColor: _isCollapsed
                  ? ThemedBackground.getAppBarColor(context, isScrolled: true)
                  : Colors.transparent,
              elevation: 0,
              centerTitle: true,
              leading: _isCollapsed
                  ? IconButton(
                      icon: const Icon(CupertinoIcons.back, color: Colors.white),
                      onPressed: () => Navigator.of(context).pop(),
                    )
                  : Container(
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withAlpha((0.3 * 255).round()),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: IconButton(
                        icon: const Icon(CupertinoIcons.back, color: Colors.white),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ),
              actions: [
                _isCollapsed
                    ? IconButton(
                        icon: Icon(
                          _isFavorite ? Icons.favorite : Icons.favorite_border,
                          color: _isFavorite ? Colors.red : Colors.white,
                        ),
                        onPressed: _toggleFavorite,
                      )
                    : Container(
                        margin: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.black.withAlpha((0.3 * 255).round()),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: IconButton(
                          icon: Icon(
                            _isFavorite ? Icons.favorite : Icons.favorite_border,
                            color: _isFavorite ? Colors.red : Colors.white,
                          ),
                          onPressed: _toggleFavorite,
                        ),
                      ),
              ],
              flexibleSpace: LayoutBuilder(
                builder: (context, constraints) {
                  final double delta = constraints.maxHeight - kToolbarHeight;
                  final bool showGradient = delta > 40;
                  return FlexibleSpaceBar(
                    background: _buildImageCarousel(),
                    title: (_isCollapsed || !showGradient)
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              // Back icon
                              Padding(
                                padding: const EdgeInsets.only(left: 4, right: 8),
                                child: IconButton(
                                  icon: const Icon(CupertinoIcons.back, color: Colors.white),
                                  onPressed: () => Navigator.of(context).pop(),
                                  tooltip: 'Back',
                                ),
                              ),
                              // Title
                              Expanded(
                                child: Text(
                                  _recipe!.title,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 20,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              // Heart icon
                              Padding(
                                padding: const EdgeInsets.only(left: 8, right: 4),
                                child: IconButton(
                                  icon: Icon(
                                    _isFavorite ? Icons.favorite : Icons.favorite_border,
                                    color: _isFavorite ? Colors.red : Colors.white,
                                  ),
                                  onPressed: _toggleFavorite,
                                  tooltip: 'Favorite',
                                ),
                              ),
                            ],
                          )
                        : Container(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.bottomCenter,
                                end: Alignment.topCenter,
                                colors: [
                                  Colors.black.withAlpha((0.8 * 255).round()),
                                  Colors.transparent,
                                ],
                              ),
                            ),
                            child: Text(
                              _recipe!.title,
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                    titlePadding: _isCollapsed
                        ? const EdgeInsets.symmetric(horizontal: 0, vertical: 8)
                        : EdgeInsets.zero,
                  );
                },
              ),
            ),

            // Recipe content as markdown
            SliverPadding(
              padding: const EdgeInsets.all(24),
              sliver: SliverList(
                delegate: SliverChildListDelegate([
                  MarkdownBody(
                    data: _buildMarkdownContent(l10n),
                    styleSheet: MarkdownStyleSheet(
                      h1: theme.textTheme.headlineLarge?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                      h2: theme.textTheme.headlineSmall?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                      h3: theme.textTheme.titleLarge?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                      p: theme.textTheme.bodyMedium,
                      listBullet: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.primary,
                      ),
                      strong: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                      blockquote: theme.textTheme.bodyMedium?.copyWith(
                        fontStyle: FontStyle.italic,
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ),
                  const SizedBox(height: 100), // Bottom padding for FAB
                ]),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () async {
          try {
            final dataSyncService = getIt<DataSyncService>();
            await dataSyncService.generateGroceryItemsFromRecipe(_recipe!.id);
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(l10n.addedIngredientsToGroceryList)),
              );
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Error: $e')),
              );
            }
          }
        },
        icon: const Icon(Icons.add_shopping_cart),
        label: Text(l10n.addToGroceryList),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: Colors.white,
      ),
    );
  }

  Widget _buildImageCarousel() {
    if (_imageUrls.isEmpty) {
      return Container(
        color: Colors.grey[300],
        child: const Center(
          child: Icon(
            Icons.restaurant,
            size: 64,
            color: Colors.grey,
          ),
        ),
      );
    }

    return Stack(
      children: [
        CarouselSlider(
          options: CarouselOptions(
            height: double.infinity,
            viewportFraction: 1.0,
            enableInfiniteScroll: _imageUrls.length > 1,
            onPageChanged: (index, reason) {
              setState(() {
                _currentImageIndex = index;
              });
            },
          ),
          items: _imageUrls.map((url) {
            return Image.network(
              url,
              fit: BoxFit.cover,
              width: double.infinity,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[300],
                  child: const Center(
                    child: Icon(
                      Icons.broken_image,
                      size: 64,
                      color: Colors.grey,
                    ),
                  ),
                );
              },
            );
          }).toList(),
        ),
        if (_imageUrls.length > 1)
          Positioned(
            bottom: 16,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: _imageUrls.asMap().entries.map((entry) {
                return Container(
                  width: 8,
                  height: 8,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _currentImageIndex == entry.key
                        ? Colors.white
                        : Colors.white.withAlpha((0.4 * 255).round()),
                  ),
                );
              }).toList(),
            ),
          ),
      ],
    );
  }

  String _buildMarkdownContent(AppLocalizations l10n) {
    final buffer = StringBuffer();

    // Metadata section
    buffer.writeln('## ${l10n.recipeDetails}');
    buffer.writeln();
    buffer.writeln('**${l10n.cookTime}:** ${_recipe!.cookingTimeMinutes} min  ');
    buffer.writeln('**${l10n.servings}:** ${_recipe!.servings}  ');
    buffer.writeln('**${l10n.difficulty}:** ${difficultyLabel(difficultyFromInt(_recipe!.difficulty))}  ');
    buffer.writeln('**${l10n.category}:** ${categoryLabel(categoryFromInt(_recipe!.category))}  ');
    buffer.writeln();

    // Ingredients section
    buffer.writeln('## ${l10n.ingredients}');
    buffer.writeln();
    for (final ingredient in _ingredients) {
      buffer.writeln('- ${ingredient.amount} ${ingredient.unit} ${ingredient.name}');
    }
    buffer.writeln();

    buffer.writeln(
      (_recipe!.instructions ?? '').replaceAll(RegExp(r'(\\n|\n)'), '\n')
    );
    buffer.writeln();

    // Nutrition section (if available)
    if (_recipe!.calories != null) {
      buffer.writeln('## ${l10n.nutritionInformation}');
      buffer.writeln();
      if (_recipe!.calories != null) {
        buffer.writeln('**${l10n.calories}:** ${_recipe!.calories} kcal  ');
      }
      if (_recipe!.protein != null) {
        buffer.writeln('**${l10n.protein}:** ${_recipe!.protein?.toStringAsFixed(1)}g  ');
      }
      if (_recipe!.carbs != null) {
        buffer.writeln('**${l10n.carbs}:** ${_recipe!.carbs?.toStringAsFixed(1)}g  ');
      }
      if (_recipe!.fat != null) {
        buffer.writeln('**${l10n.fat}:** ${_recipe!.fat?.toStringAsFixed(1)}g  ');
      }
      if (_recipe!.fiber != null) {
        buffer.writeln('**${l10n.fiber}:** ${_recipe!.fiber?.toStringAsFixed(1)}g  ');
      }
      if (_recipe!.sugar != null) {
        buffer.writeln('**${l10n.sugar}:** ${_recipe!.sugar?.toStringAsFixed(1)}g  ');
      }
      if (_recipe!.sodium != null) {
        buffer.writeln('**${l10n.sodium}:** ${_recipe!.sodium?.toStringAsFixed(0)}mg  ');
      }
    }

    return buffer.toString();
  }



}
