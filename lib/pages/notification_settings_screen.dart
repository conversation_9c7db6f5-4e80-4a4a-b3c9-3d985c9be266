import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:ailacarte/widgets/themed_background.dart';
import 'package:ailacarte/data/settings_preference.dart';
import 'package:ailacarte/theme/custom_theme.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  bool _isLoading = true;
  final Map<String, bool> _notificationStates = {};
  final Map<String, String> _notificationTimes = {};
  final Map<String, String> _notificationFrequencies = {};

  @override
  void initState() {
    super.initState();
    _loadNotificationSettings();
  }

  Future<void> _loadNotificationSettings() async {
    setState(() => _isLoading = true);
    
    try {
      for (final type in SettingsPreferences.notificationTypes) {
        _notificationStates[type] = await SettingsPreferences.getNotificationEnabled(type);
        _notificationTimes[type] = await SettingsPreferences.getNotificationTime(type);
        _notificationFrequencies[type] = await SettingsPreferences.getNotificationFrequency(type);
      }
    } catch (e) {
      debugPrint('Error loading notification settings: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _updateNotificationEnabled(String type, bool enabled) async {
    try {
      await SettingsPreferences.setNotificationEnabled(type, enabled);
      setState(() {
        _notificationStates[type] = enabled;
      });
    } catch (e) {
      debugPrint('Error updating notification setting: $e');
    }
  }

  Future<void> _updateNotificationTime(String type, String time) async {
    try {
      await SettingsPreferences.setNotificationTime(type, time);
      setState(() {
        _notificationTimes[type] = time;
      });
    } catch (e) {
      debugPrint('Error updating notification time: $e');
    }
  }

  String _getNotificationTypeName(String type) {
    switch (type) {
      case SettingsPreferences.notificationTypeRecipe:
        return 'Recipe Reminders';
      case SettingsPreferences.notificationTypeMealPlan:
        return 'Meal Planning';
      case SettingsPreferences.notificationTypeGrocery:
        return 'Grocery Lists';
      default:
        return type;
    }
  }

  String _getNotificationTypeDescription(String type) {
    switch (type) {
      case SettingsPreferences.notificationTypeRecipe:
        return 'Get reminded to try new recipes';
      case SettingsPreferences.notificationTypeMealPlan:
        return 'Plan your meals for the week';
      case SettingsPreferences.notificationTypeGrocery:
        return 'Remember to check your grocery lists';
      default:
        return 'Notification settings';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;

    return ThemedBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          backgroundColor: ThemedBackground.getAppBarColor(context, isScrolled: true),
          title: Text(
            'Notification Settings',
            style: theme.textTheme.titleLarge,
          ),
          leading: IconButton(
            icon: const Icon(CupertinoIcons.back),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _buildBody(theme, localizations),
      ),
    );
  }

  Widget _buildBody(ThemeData theme, AppLocalizations localizations) {
    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 600),
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Text(
                  'Manage your notification preferences',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: CustomTheme.getSecondaryTextColor(context),
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Notification types
              ...SettingsPreferences.notificationTypes.map((type) =>
                _buildNotificationSection(type, theme)
              ),
              
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationSection(String type, ThemeData theme) {
    final isEnabled = _notificationStates[type] ?? false;
    final time = _notificationTimes[type] ?? SettingsPreferences.defaultTime;
    final isDarkMode = theme.brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: CustomTheme.getShadowColor(context, opacity: 0.05),
              blurRadius: 8,
              spreadRadius: 0,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
            child: Container(
              decoration: BoxDecoration(
                color: theme.cardColor.withAlpha(100),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isDarkMode
                      ? Colors.white.withAlpha(20)
                      : Colors.black.withAlpha(10),
                  width: 0.5,
                ),
              ),
              child: Column(
                children: [
                  // Main toggle
                  ListTile(
                    title: Text(
                      _getNotificationTypeName(type),
                      style: TextStyle(
                        color: CustomTheme.getPrimaryTextColor(context).withAlpha(220),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    subtitle: Text(
                      _getNotificationTypeDescription(type),
                      style: TextStyle(
                        color: CustomTheme.getSecondaryTextColor(context).withAlpha(180),
                        fontSize: 13,
                      ),
                    ),
                    trailing: Switch(
                      value: isEnabled,
                      onChanged: (value) => _updateNotificationEnabled(type, value),
                    ),
                  ),
                  
                  // Time setting (only show if enabled)
                  if (isEnabled) ...[
                    Divider(
                      height: 1,
                      indent: 16,
                      endIndent: 16,
                      color: isDarkMode 
                          ? Colors.white.withAlpha(20) 
                          : Colors.black.withAlpha(10),
                    ),
                    ListTile(
                      leading: Icon(
                        Icons.access_time,
                        color: theme.colorScheme.primary.withAlpha(220),
                      ),
                      title: Text(
                        'Time',
                        style: TextStyle(
                          color: CustomTheme.getPrimaryTextColor(context).withAlpha(220),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      subtitle: Text(
                        _formatTime(time),
                        style: TextStyle(
                          color: CustomTheme.getSecondaryTextColor(context).withAlpha(180),
                          fontSize: 13,
                        ),
                      ),
                      onTap: () => _showTimePicker(type, time),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  String _formatTime(String time24) {
    try {
      final parts = time24.split(':');
      if (parts.length < 2) return time24;
      
      final hour = int.tryParse(parts[0]) ?? 0;
      final minute = int.tryParse(parts[1]) ?? 0;

      final period = hour >= 12 ? 'PM' : 'AM';
      final hour12 = hour % 12 == 0 ? 12 : hour % 12;
      final minuteStr = minute.toString().padLeft(2, '0');

      return '$hour12:$minuteStr $period';
    } catch (e) {
      return time24;
    }
  }

  Future<void> _showTimePicker(String type, String currentTime) async {
    final parts = currentTime.split(':');
    final hour = int.tryParse(parts[0]) ?? 9;
    final minute = int.tryParse(parts[1]) ?? 0;

    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay(hour: hour, minute: minute),
    );

    if (picked != null) {
      final newTime = '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';
      await _updateNotificationTime(type, newTime);
    }
  }
}
