import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ailacarte/pages/onboarding/welcome_screen.dart';
import 'package:ailacarte/pages/onboarding/theme_screen.dart';
import 'package:ailacarte/pages/onboarding/notifications_screen.dart';
import 'package:ailacarte/pages/onboarding/features_screen.dart';

import 'package:ailacarte/data/settings_preference.dart';
import 'package:ailacarte/services/analytics_service.dart';

class OnboardingScreen extends StatefulWidget {
  final VoidCallback onComplete;

  const OnboardingScreen({
    super.key,
    required this.onComplete,
  });

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<String> _stepNames = [
    'welcome',
    'theme',
    'notifications',
    'features',
  ];

  @override
  void initState() {
    super.initState();
    // Log onboarding start
    AnalyticsService.instance.logOnboardingStep('started');
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _stepNames.length - 1) {
      // Log current step completion
      AnalyticsService.instance.logOnboardingStep(_stepNames[_currentPage]);
      
      setState(() {
        _currentPage++;
      });
      
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      setState(() {
        _currentPage--;
      });
      
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _completeOnboarding() async {
    try {
      // Mark onboarding as completed
      await SettingsPreferences.setOnboardingCompleted(true);
      
      // Log onboarding completion
      await AnalyticsService.instance.logOnboardingCompleted();
      
      // Navigate to paywall
      if (mounted) {
        context.go('/paywall');
      }
    } catch (e) {
      debugPrint('Error completing onboarding: $e');
      // Still proceed even if there's an error
      if (mounted) {
        context.go('/paywall');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Main content
          PageView(
            controller: _pageController,
            physics: const NeverScrollableScrollPhysics(), // Disable swipe
            children: [
              WelcomeScreen(onNext: _nextPage),
              ThemeScreen(onNext: _nextPage),
              NotificationsScreen(onNext: _nextPage),
              FeaturesScreen(onNext: _nextPage),
            ],
          ),
          
          // Progress indicator
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            left: 24,
            right: 24,
            child: _buildProgressIndicator(),
          ),
          
          // Back button (except on first page)
          if (_currentPage > 0)
            Positioned(
              top: MediaQuery.of(context).padding.top + 16,
              left: 24,
              child: IconButton(
                onPressed: _previousPage,
                icon: const Icon(Icons.arrow_back),
                style: IconButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.surface.withAlpha((0.8 * 255).round()),
                  foregroundColor: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    final theme = Theme.of(context);
    final progress = (_currentPage + 1) / _stepNames.length;

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Step ${_currentPage + 1} of ${_stepNames.length}',
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${(progress * 100).round()}%',
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: theme.colorScheme.surfaceContainerHighest,
          valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
          borderRadius: BorderRadius.circular(4),
        ),
      ],
    );
  }
}
