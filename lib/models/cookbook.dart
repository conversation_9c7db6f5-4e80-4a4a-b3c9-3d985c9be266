import 'package:equatable/equatable.dart';

class Cookbook extends Equatable {
  final String id;
  final String name;
  final String description;
  final String? imageUrl;
  final String? userId; // For user-specific cookbooks
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isDefault; // For the default "AI La Carte recipes" cookbook

  const Cookbook({
    required this.id,
    required this.name,
    required this.description,
    this.imageUrl,
    this.userId,
    required this.createdAt,
    required this.updatedAt,
    this.isDefault = false,
  });

  Cookbook copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    String? userId,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isDefault,
  }) {
    return Cookbook(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDefault: isDefault ?? this.isDefault,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'image_url': imageUrl,
      'user_id': userId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_default': isDefault,
    };
  }

  factory Cookbook.fromJson(Map<String, dynamic> json) {
    return Cookbook(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      imageUrl: json['image_url'] as String?,
      userId: json['user_id'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      isDefault: json['is_default'] as bool? ?? false,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        imageUrl,
        userId,
        createdAt,
        updatedAt,
        isDefault,
      ];
}
