enum GroceryCategory {
  vegetables,
  fruits,
  meat,
  dairy,
  pantry,
  bakery,
  frozen,
  beverages,
  snacks,
  spices,
  other;

  String get value {
    switch (this) {
      case GroceryCategory.vegetables:
        return 'Vegetables';
      case GroceryCategory.fruits:
        return 'Fruits';
      case GroceryCategory.meat:
        return 'Meat';
      case GroceryCategory.dairy:
        return 'Dairy';
      case GroceryCategory.pantry:
        return 'Pantry';
      case GroceryCategory.bakery:
        return 'Bakery';
      case GroceryCategory.frozen:
        return 'Frozen';
      case GroceryCategory.beverages:
        return 'Beverages';
      case GroceryCategory.snacks:
        return 'Snacks';
      case GroceryCategory.spices:
        return 'Spices';
      case GroceryCategory.other:
        return 'Other';
    }
  }

  static GroceryCategory fromString(String value) {
    for (final category in GroceryCategory.values) {
      if (category.value == value) {
        return category;
      }
    }
    return GroceryCategory.other;
  }
}

enum GroceryUnit {
  pieces,
  pounds,
  ounces,
  cups,
  tablespoons,
  teaspoons,
  liters,
  milliliters,
  kilograms,
  grams,
  dozen,
  bunch,
  bag,
  box,
  can,
  bottle;

  String get value {
    switch (this) {
      case GroceryUnit.pieces:
        return 'pieces';
      case GroceryUnit.pounds:
        return 'lbs';
      case GroceryUnit.ounces:
        return 'oz';
      case GroceryUnit.cups:
        return 'cups';
      case GroceryUnit.tablespoons:
        return 'tbsp';
      case GroceryUnit.teaspoons:
        return 'tsp';
      case GroceryUnit.liters:
        return 'liters';
      case GroceryUnit.milliliters:
        return 'ml';
      case GroceryUnit.kilograms:
        return 'kg';
      case GroceryUnit.grams:
        return 'g';
      case GroceryUnit.dozen:
        return 'dozen';
      case GroceryUnit.bunch:
        return 'bunch';
      case GroceryUnit.bag:
        return 'bag';
      case GroceryUnit.box:
        return 'box';
      case GroceryUnit.can:
        return 'can';
      case GroceryUnit.bottle:
        return 'bottle';
    }
  }

  static GroceryUnit fromString(String value) {
    for (final unit in GroceryUnit.values) {
      if (unit.value == value) {
        return unit;
      }
    }
    return GroceryUnit.pieces;
  }
}
