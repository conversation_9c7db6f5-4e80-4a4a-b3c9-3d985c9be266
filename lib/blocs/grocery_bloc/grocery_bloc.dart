import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ailacarte/blocs/grocery_bloc/grocery_event.dart';
import 'package:ailacarte/blocs/grocery_bloc/grocery_state.dart';
import 'package:ailacarte/repositories/grocery_repository.dart';
import 'package:ailacarte/database/database.dart';
import 'package:drift/drift.dart';
import 'package:uuid/uuid.dart';

class GroceryBloc extends Bloc<GroceryEvent, GroceryState> {
  final GroceryRepository _repository;
  final Uuid _uuid = const Uuid();

  GroceryBloc({required GroceryRepository repository})
      : _repository = repository,
        super(const GroceryState()) {
    on<LoadGroceryItems>(_onLoadGroceryItems);
    on<AddGroceryItem>(_onAddGroceryItem);
    on<UpdateGroceryItem>(_onUpdateGroceryItem);
    on<ToggleGroceryItem>(_onToggleGroceryItem);
    on<DeleteGroceryItem>(_onDeleteGroceryItem);
    on<FilterGroceryItems>(_onFilterGroceryItems);
  }

  Future<void> _onLoadGroceryItems(
    LoadGroceryItems event,
    Emitter<GroceryState> emit,
  ) async {
    emit(state.copyWith(status: GroceryStatus.loading));
    
    try {
      final items = await _repository.getAllGroceryItems();
      final recipes = await _repository.getRecipesWithGroceryItems();
      
      // Build filter list with recipes
      final filters = ['All', 'Other'];
      for (final recipe in recipes) {
        if (!filters.contains(recipe.title)) {
          filters.add(recipe.title);
        }
      }
      
      final filteredItems = _applyFilter(items, state.selectedFilter);
      
      emit(state.copyWith(
        status: GroceryStatus.success,
        items: items,
        filteredItems: filteredItems,
        availableFilters: filters,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: GroceryStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onAddGroceryItem(
    AddGroceryItem event,
    Emitter<GroceryState> emit,
  ) async {
    try {
      final now = DateTime.now();
      final newItem = GroceryItemsCompanion.insert(
        id: _uuid.v4(),
        name: event.name,
        quantity: event.quantity,
        category: event.category,
        notes: Value(event.notes),
        recipeId: Value(event.recipeId),
        createdAt: now,
      );

      await _repository.insertGroceryItem(newItem);
      add(LoadGroceryItems()); // Reload items
    } catch (e) {
      emit(state.copyWith(
        status: GroceryStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onUpdateGroceryItem(
    UpdateGroceryItem event,
    Emitter<GroceryState> emit,
  ) async {
    try {
      final updatedItem = GroceryItemsCompanion(
        id: Value(event.id),
        name: Value(event.name),
        quantity: Value(event.quantity),
        category: Value(event.category),
        notes: Value(event.notes),
      );

      await _repository.updateGroceryItem(updatedItem);
      add(LoadGroceryItems()); // Reload items
    } catch (e) {
      emit(state.copyWith(
        status: GroceryStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onToggleGroceryItem(
    ToggleGroceryItem event,
    Emitter<GroceryState> emit,
  ) async {
    try {
      final item = state.items.firstWhere((item) => item.id == event.id);
      final updatedItem = GroceryItemsCompanion(
        id: Value(event.id),
        isCompleted: Value(!item.isCompleted),
      );

      await _repository.updateGroceryItem(updatedItem);
      add(LoadGroceryItems()); // Reload items
    } catch (e) {
      emit(state.copyWith(
        status: GroceryStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onDeleteGroceryItem(
    DeleteGroceryItem event,
    Emitter<GroceryState> emit,
  ) async {
    try {
      await _repository.deleteGroceryItem(event.id);
      add(LoadGroceryItems()); // Reload items
    } catch (e) {
      emit(state.copyWith(
        status: GroceryStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  void _onFilterGroceryItems(
    FilterGroceryItems event,
    Emitter<GroceryState> emit,
  ) {
    final filteredItems = _applyFilter(state.items, event.filter);
    emit(state.copyWith(
      selectedFilter: event.filter,
      filteredItems: filteredItems,
    ));
  }

  List<GroceryItem> _applyFilter(List<GroceryItem> items, String filter) {
    if (filter == 'All') {
      return items;
    } else if (filter == 'Other') {
      return items.where((item) => item.recipeId == null).toList();
    } else {
      // Filter by recipe name - need to get recipe by name
      return items.where((item) => item.recipeId != null).toList();
    }
  }
}
