import 'package:equatable/equatable.dart';
import 'package:ailacarte/database/database.dart';

enum GroceryStatus { initial, loading, success, failure }

class GroceryState extends Equatable {
  final GroceryStatus status;
  final List<GroceryItem> items;
  final List<GroceryItem> filteredItems;
  final String selectedFilter;
  final List<String> availableFilters;
  final String? errorMessage;

  const GroceryState({
    this.status = GroceryStatus.initial,
    this.items = const [],
    this.filteredItems = const [],
    this.selectedFilter = 'All',
    this.availableFilters = const ['All', 'Other'],
    this.errorMessage,
  });

  GroceryState copyWith({
    GroceryStatus? status,
    List<GroceryItem>? items,
    List<GroceryItem>? filteredItems,
    String? selectedFilter,
    List<String>? availableFilters,
    String? errorMessage,
  }) {
    return GroceryState(
      status: status ?? this.status,
      items: items ?? this.items,
      filteredItems: filteredItems ?? this.filteredItems,
      selectedFilter: selectedFilter ?? this.selectedFilter,
      availableFilters: availableFilters ?? this.availableFilters,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [
        status,
        items,
        filteredItems,
        selectedFilter,
        availableFilters,
        errorMessage,
      ];
}
