import 'package:equatable/equatable.dart';

abstract class GroceryEvent extends Equatable {
  const GroceryEvent();

  @override
  List<Object?> get props => [];
}

class LoadGroceryItems extends GroceryEvent {}

class AddGroceryItem extends GroceryEvent {
  final String name;
  final String quantity;
  final String category;
  final String? notes;
  final String? recipeId;

  const AddGroceryItem({
    required this.name,
    required this.quantity,
    required this.category,
    this.notes,
    this.recipeId,
  });

  @override
  List<Object?> get props => [name, quantity, category, notes, recipeId];
}

class UpdateGroceryItem extends GroceryEvent {
  final String id;
  final String name;
  final String quantity;
  final String category;
  final String? notes;

  const UpdateGroceryItem({
    required this.id,
    required this.name,
    required this.quantity,
    required this.category,
    this.notes,
  });

  @override
  List<Object?> get props => [id, name, quantity, category, notes];
}

class ToggleGroceryItem extends GroceryEvent {
  final String id;

  const ToggleGroceryItem(this.id);

  @override
  List<Object?> get props => [id];
}

class DeleteGroceryItem extends GroceryEvent {
  final String id;

  const DeleteGroceryItem(this.id);

  @override
  List<Object?> get props => [id];
}

class FilterGroceryItems extends GroceryEvent {
  final String filter;

  const FilterGroceryItems(this.filter);

  @override
  List<Object?> get props => [filter];
}
