import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ailacarte/blocs/cookbook_bloc/cookbook_event.dart';
import 'package:ailacarte/blocs/cookbook_bloc/cookbook_state.dart';
import 'package:ailacarte/domain/repositories/cookbook_repository.dart';

class CookbookBloc extends Bloc<CookbookEvent, CookbookState> {
  final CookbookRepository _cookbookRepository;

  CookbookBloc(this._cookbookRepository) : super(const CookbookState()) {
    on<LoadCookbooksEvent>(_onLoadCookbooks);
    on<SelectCookbookEvent>(_onSelectCookbook);
    on<CreateCookbookEvent>(_onCreateCookbook);
    on<UpdateCookbookEvent>(_onUpdateCookbook);
    on<DeleteCookbookEvent>(_onDeleteCookbook);
  }

  Future<void> _onLoadCookbooks(
    LoadCookbooksEvent event,
    Emitter<CookbookState> emit,
  ) async {
    emit(state.copyWith(status: CookbookStatus.loading));
    
    try {
      final cookbooks = await _cookbookRepository.getCookbooks();
      
      // Select default cookbook if no cookbook is selected
      var selectedCookbook = state.selectedCookbook;
      if (selectedCookbook == null && cookbooks.isNotEmpty) {
        selectedCookbook = cookbooks.firstWhere(
          (cookbook) => cookbook.isDefault,
          orElse: () => cookbooks.first,
        );
      }
      
      emit(state.copyWith(
        status: CookbookStatus.success,
        cookbooks: cookbooks,
        selectedCookbook: selectedCookbook,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: CookbookStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onSelectCookbook(
    SelectCookbookEvent event,
    Emitter<CookbookState> emit,
  ) async {
    try {
      final cookbook = await _cookbookRepository.getCookbookById(event.cookbookId);
      if (cookbook != null) {
        emit(state.copyWith(selectedCookbook: cookbook));
      }
    } catch (e) {
      emit(state.copyWith(
        status: CookbookStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onCreateCookbook(
    CreateCookbookEvent event,
    Emitter<CookbookState> emit,
  ) async {
    emit(state.copyWith(status: CookbookStatus.loading));
    
    try {
      final newCookbook = await _cookbookRepository.createCookbook(event.cookbook);
      final updatedCookbooks = [...state.cookbooks, newCookbook];
      
      emit(state.copyWith(
        status: CookbookStatus.success,
        cookbooks: updatedCookbooks,
        selectedCookbook: newCookbook,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: CookbookStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onUpdateCookbook(
    UpdateCookbookEvent event,
    Emitter<CookbookState> emit,
  ) async {
    try {
      final updatedCookbook = await _cookbookRepository.updateCookbook(event.cookbook);
      final updatedCookbooks = state.cookbooks
          .map((cookbook) => cookbook.id == updatedCookbook.id ? updatedCookbook : cookbook)
          .toList();
      
      emit(state.copyWith(
        cookbooks: updatedCookbooks,
        selectedCookbook: state.selectedCookbook?.id == updatedCookbook.id 
            ? updatedCookbook 
            : state.selectedCookbook,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: CookbookStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onDeleteCookbook(
    DeleteCookbookEvent event,
    Emitter<CookbookState> emit,
  ) async {
    try {
      await _cookbookRepository.deleteCookbook(event.cookbookId);
      final updatedCookbooks = state.cookbooks
          .where((cookbook) => cookbook.id != event.cookbookId)
          .toList();
      
      // If deleted cookbook was selected, select the first available cookbook
      var selectedCookbook = state.selectedCookbook;
      if (selectedCookbook?.id == event.cookbookId) {
        selectedCookbook = updatedCookbooks.isNotEmpty ? updatedCookbooks.first : null;
      }
      
      emit(state.copyWith(
        cookbooks: updatedCookbooks,
        selectedCookbook: selectedCookbook,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: CookbookStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }
}
