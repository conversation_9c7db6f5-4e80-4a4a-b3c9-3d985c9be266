import 'dart:ui';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:ailacarte/data/settings_preference.dart';

// Events
abstract class LanguageEvent extends Equatable {
  const LanguageEvent();

  @override
  List<Object> get props => [];
}

class ChangeLanguageEvent extends LanguageEvent {
  final Locale locale;

  const ChangeLanguageEvent(this.locale);

  @override
  List<Object> get props => [locale];
}

class LoadLanguageEvent extends LanguageEvent {}

// State
class LanguageState extends Equatable {
  final Locale locale;

  const LanguageState({required this.locale});

  @override
  List<Object> get props => [locale];
}

// Bloc
class LanguageBloc extends Bloc<LanguageEvent, LanguageState> {
  LanguageBloc() : super(const LanguageState(locale: Locale('en'))) {
    on<LoadLanguageEvent>(_onLoadLanguage);
    on<ChangeLanguageEvent>(_onChangeLanguage);

    // Load language on initialization
    add(LoadLanguageEvent());
  }

  Future<void> _onLoadLanguage(LoadLanguageEvent event, Emitter<LanguageState> emit) async {
    final languageCode = await SettingsPreferences.getLanguage();
    emit(LanguageState(locale: Locale(languageCode)));
  }

  Future<void> _onChangeLanguage(ChangeLanguageEvent event, Emitter<LanguageState> emit) async {
    await SettingsPreferences.setLanguage(event.locale.languageCode);
    emit(LanguageState(locale: event.locale));
  }
}
