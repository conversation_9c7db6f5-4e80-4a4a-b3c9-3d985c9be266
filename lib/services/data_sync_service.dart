import 'dart:convert';
import 'package:drift/drift.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ailacarte/database/database.dart';
import 'package:ailacarte/di/service_locator.dart';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';

class DataSyncService {
  static final DataSyncService _instance = DataSyncService._internal();
  factory DataSyncService() => _instance;
  DataSyncService._internal();

  final AppDatabase _database = getIt<AppDatabase>();
  final SupabaseClient _supabase = Supabase.instance.client;
  final Uuid _uuid = const Uuid();

  static const String _defaultCookbookId = 'default-cookbook';

  /// Sync all data from Supabase to local database
  Future<void> syncFromSupabase({
    required String defaultCookbookName,
    required String defaultCookbookDescription,
    required int defaultCookbookColor,
  }) async {
    try {
      debugPrint('Starting data sync from Supabase...');
      
      // Ensure default cookbook exists
      final defaultCookbook = await _database.getDefaultCookbook();
      if (defaultCookbook == null) {
        await _database.insertCookbook(CookbooksCompanion.insert(
          id: _defaultCookbookId,
          name: defaultCookbookName,
          description: defaultCookbookDescription,
          color: Value(defaultCookbookColor),
          isDefault: const Value(true),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ));
      }
      
      // Sync recipes
      await _syncRecipes();
      
      debugPrint('Data sync completed successfully');
    } catch (e) {
      debugPrint('Error syncing data from Supabase: $e');
      rethrow;
    }
  }

  /// Sync recipes from Supabase
  Future<void> _syncRecipes() async {
    try {
      // Fetch recipes from Supabase
      final response = await _supabase
          .from('aila_common_recipes')
          .select('*')
          .order('created_at', ascending: false);

      for (final recipeData in response) {
        // Insert/update recipe
        final recipe = RecipesCompanion.insert(
          id: recipeData['id'] as String,
          title: recipeData['title'] as String,
          description: recipeData['description'] as String? ?? '',
          instructions: recipeData['instructions'] as String? ?? '',
          cookingTimeMinutes: recipeData['cooking_minutes'] as int? ?? 0,
          servings: recipeData['servings'] as int? ?? 1,
          difficulty: recipeData['difficulty'] as int? ?? 0, // enum as int
          category: recipeData['category'] as int? ?? 0, // enum as int
          imageUrls: Value(recipeData['image_urls'] != null
              ? jsonEncode(recipeData['image_urls'])
              : null),
          tags: recipeData['tags'] != null
              ? recipeData['tags'] as String // comma-separated string
              : '',
          cookbookId: recipeData['cookbook_id'] as String? ?? _defaultCookbookId, // Default cookbook for synced recipes
          createdAt: DateTime.parse(recipeData['created_at'] as String),
          calories: Value(recipeData['calories'] as int?),
          protein: Value((recipeData['protein'] as num?)?.toDouble()),
          carbs: Value((recipeData['carbs'] as num?)?.toDouble()),
          fat: Value((recipeData['fat'] as num?)?.toDouble()),
          fiber: Value((recipeData['fiber'] as num?)?.toDouble()),
          sugar: Value((recipeData['sugar'] as num?)?.toDouble()),
          sodium: Value((recipeData['sodium'] as num?)?.toDouble()),
        );

        await _database.into(_database.recipes).insertOnConflictUpdate(recipe);

        // Sync ingredients for this recipe
        await _database.deleteRecipeIngredients(recipeData['id'] as String);
        
        // Parse ingredients JSON array
        final ingredients = recipeData['ingredients'] as List<dynamic>? ?? [];
        for (final ingredientData in ingredients) {
          if (ingredientData is Map<String, dynamic>) {
            final ingredient = IngredientsCompanion.insert(
              id: ingredientData['id'] as String? ?? _uuid.v4(),
              recipeId: recipeData['id'] as String,
              name: ingredientData['name'] as String? ?? '',
              amount: ingredientData['amount'] is int
                  ? ingredientData['amount'] as int
                  : int.tryParse(ingredientData['amount'].toString()) ?? 0,
              unit: ingredientData['unit'] as String? ?? '',
              order: ingredientData['order'] as int? ?? 0,
              createdAt: ingredientData['created_at'] != null
                  ? DateTime.parse(ingredientData['created_at'] as String)
                  : DateTime.now(),
            );
            await _database.into(_database.ingredients).insertOnConflictUpdate(ingredient);
          } else if (ingredientData is String) {
            final ingredient = IngredientsCompanion.insert(
              id: _uuid.v4(),
              recipeId: recipeData['id'] as String,
              name: ingredientData,
              amount: 0,
              unit: '',
              order: 0,
              createdAt: DateTime.now(),
            );
            await _database.into(_database.ingredients).insertOnConflictUpdate(ingredient);
          }
        }
      }
    } catch (e) {
      debugPrint('Error syncing recipes: $e');
      rethrow;
    }
  }


  /// Upload local grocery item to Supabase
  Future<void> uploadGroceryItem(GroceryItem item) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) return;

      await _supabase.from('grocery_items').upsert({
        'id': item.id,
        'name': item.name,
        'quantity': item.quantity,
        'category': item.category,
        'is_completed': item.isCompleted,
        'notes': item.notes,
        'recipe_id': item.recipeId,
        'created_at': item.createdAt.toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error uploading grocery item: $e');
      rethrow;
    }
  }

  /// Delete grocery item from Supabase
  Future<void> deleteGroceryItemFromSupabase(String id) async {
    try {
      await _supabase.from('grocery_items').delete().eq('id', id);
    } catch (e) {
      debugPrint('Error deleting grocery item from Supabase: $e');
      rethrow;
    }
  }

  /// Generate ingredients as grocery items from a recipe
  Future<List<GroceryItem>> generateGroceryItemsFromRecipe(String recipeId) async {
    try {
      final ingredients = await _database.getRecipeIngredients(recipeId);
      final groceryItems = <GroceryItem>[];
      final now = DateTime.now();

      for (final ingredient in ingredients) {
        final groceryItem = GroceryItem(
          id: _uuid.v4(),
          name: ingredient.name,
          quantity: '${ingredient.amount} ${ingredient.unit}',
          category: _categorizeIngredient(ingredient.name),
          isCompleted: false,
          notes: 'From recipe',
          recipeId: recipeId,
          createdAt: now,
        );
        
        groceryItems.add(groceryItem);
        
        // Insert into local database
        await _database.insertGroceryItem(GroceryItemsCompanion.insert(
          id: groceryItem.id,
          name: groceryItem.name,
          quantity: groceryItem.quantity,
          category: groceryItem.category,
          isCompleted: Value(groceryItem.isCompleted),
          notes: Value(groceryItem.notes),
          recipeId: Value(groceryItem.recipeId),
          createdAt: groceryItem.createdAt,
        ));
        
        // Upload to Supabase if user is authenticated
        if (_supabase.auth.currentUser != null) {
          await uploadGroceryItem(groceryItem);
        }
      }

      return groceryItems;
    } catch (e) {
      debugPrint('Error generating grocery items from recipe: $e');
      rethrow;
    }
  }

  /// Simple ingredient categorization
  String _categorizeIngredient(String ingredientName) {
    final name = ingredientName.toLowerCase();
    
    if (name.contains('chicken') || name.contains('beef') || name.contains('pork') || 
        name.contains('fish') || name.contains('meat')) {
      return 'Meat';
    } else if (name.contains('milk') || name.contains('cheese') || name.contains('yogurt') || 
               name.contains('butter') || name.contains('cream')) {
      return 'Dairy';
    } else if (name.contains('tomato') || name.contains('onion') || name.contains('carrot') || 
               name.contains('pepper') || name.contains('lettuce') || name.contains('spinach')) {
      return 'Vegetables';
    } else if (name.contains('apple') || name.contains('banana') || name.contains('orange') || 
               name.contains('berry') || name.contains('grape')) {
      return 'Fruits';
    } else if (name.contains('bread') || name.contains('roll') || name.contains('bun')) {
      return 'Bakery';
    } else if (name.contains('pasta') || name.contains('rice') || name.contains('flour') || 
               name.contains('sugar') || name.contains('salt') || name.contains('oil')) {
      return 'Pantry';
    } else {
      return 'Other';
    }
  }
}
