import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ailacarte/services/auth_service.dart';
import 'package:ailacarte/services/notification_service.dart';
import 'package:ailacarte/services/revenue_cat_service.dart';
import 'package:ailacarte/services/analytics_service.dart';

import 'package:ailacarte/database/database.dart';
import 'package:ailacarte/services/data_sync_service.dart';
import 'package:ailacarte/domain/repositories/cookbook_repository.dart';
import 'package:ailacarte/repositories/grocery_repository.dart';
import 'package:ailacarte/data/repositories/cookbook_repository_impl.dart';
import 'package:ailacarte/blocs/cookbook_bloc/cookbook_bloc.dart';
import 'package:ailacarte/blocs/grocery_bloc/grocery_bloc.dart';

final GetIt getIt = GetIt.instance;

Future<void> setupServiceLocator() async {
  // Register SharedPreferences asynchronously
  final sharedPreferences = await SharedPreferences.getInstance();
  getIt.registerSingleton<SharedPreferences>(sharedPreferences);

  // Register database
  getIt.registerLazySingleton<AppDatabase>(() => AppDatabase());

  // Register Repositories
  getIt.registerLazySingleton<CookbookRepository>(() => CookbookRepositoryImpl(getIt<AppDatabase>()));
  getIt.registerLazySingleton<GroceryRepository>(() => GroceryRepositoryImpl(database: getIt<AppDatabase>()));

  // Register BLoCs
  getIt.registerFactory<CookbookBloc>(() => CookbookBloc(getIt<CookbookRepository>()));
  getIt.registerFactory<GroceryBloc>(() => GroceryBloc(repository: getIt<GroceryRepository>()));

  // Register Services as Lazy Singletons to handle circular dependencies
  getIt.registerLazySingleton<AuthService>(() => AuthService());
  getIt.registerLazySingleton<NotificationService>(() => NotificationService());
  getIt.registerLazySingleton<RevenueCatService>(() => RevenueCatService());
  getIt.registerLazySingleton<AnalyticsService>(() => AnalyticsService());
  getIt.registerLazySingleton<DataSyncService>(() => DataSyncService());

  // Initialize services that need it
  await getIt.allReady();
}
