import 'package:flutter/foundation.dart';

class AppConfig {
  // App Information
  static const String appName = 'AI La Carte';
  static const String appVersion = '1.0.0';
  static const String buildNumber = '1';
  
  // Environment
  static bool get isDebug => kDebugMode;
  static bool get isRelease => kReleaseMode;
  static bool get isProfile => kProfileMode;
  
  // API Configuration
  static const int apiTimeoutSeconds = 30;
  static const int maxRetryAttempts = 3;
  
  // Cache Configuration
  static const int cacheExpirationHours = 24;
  static const int maxCacheSize = 100; // MB
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // These are kept for backward compatibility but should use enums instead
  
  // Notification Configuration
  static const String notificationChannelId = 'recipe_channel';
  static const String notificationChannelName = 'Recipe Notifications';
  static const String notificationChannelDescription = 'Notifications for recipe reminders and updates';
  
  // Deep Link Configuration
  static const String deepLinkScheme = 'ailacarte';
  static const String deepLinkHost = 'app';
  
  // Premium Features
  static const List<String> premiumFeatures = [
    'Unlimited AI Recipes',
    'Cloud Sync',
    'Offline Access',
    'Priority Support',
    'Advanced Search',
    'Recipe Collections',
    'Meal Planning',
    'Nutrition Analysis',
  ];
  
  // Analytics Events
  static const String eventRecipeViewed = 'recipe_viewed';
  static const String eventRecipeCreated = 'recipe_created';
  static const String eventRecipeFavorited = 'recipe_favorited';
  static const String eventGroceryItemAdded = 'grocery_item_added';
  static const String eventGroceryItemCompleted = 'grocery_item_completed';
  static const String eventPremiumUpgrade = 'premium_upgrade';
  static const String eventOnboardingCompleted = 'onboarding_completed';
  
  // Error Messages
  static const String errorNetworkUnavailable = 'Network connection unavailable';
  static const String errorServerError = 'Server error occurred';
  static const String errorUnknown = 'An unknown error occurred';
  static const String errorAuthRequired = 'Authentication required';
  static const String errorPermissionDenied = 'Permission denied';
}
