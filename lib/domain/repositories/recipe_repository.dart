import 'package:ailacarte/models/recipe.dart';

abstract class RecipeRepository {
  /// Get all recipes for the current user
  Future<List<Recipe>> getRecipes();

  /// Get recipes by cookbook
  Future<List<Recipe>> getRecipesByCookbook(String cookbookId);

  /// Get a specific recipe by ID
  Future<Recipe?> getRecipeById(String id);

  /// Search recipes by query
  Future<List<Recipe>> searchRecipes(String query);

  /// Get recipes by category
  Future<List<Recipe>> getRecipesByCategory(String category);

  /// Get favorite recipes
  Future<List<Recipe>> getFavoriteRecipes();

  /// Create a new recipe
  Future<Recipe> createRecipe(Recipe recipe);

  /// Update an existing recipe
  Future<Recipe> updateRecipe(Recipe recipe);

  /// Delete a recipe
  Future<void> deleteRecipe(String id);

  /// Toggle favorite status
  Future<Recipe> toggleFavorite(String id);

  /// Get all categories
  Future<List<String>> getCategories();

  /// Sync recipes with remote server
  Future<void> syncRecipes();
}
