import 'package:flutter/material.dart';

class ThemedBackground extends StatelessWidget {
  final Widget child;
  final bool showGradient;

  const ThemedBackground({
    super.key,
    required this.child,
    this.showGradient = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bool isDarkMode = theme.brightness == Brightness.dark;

    // Use different alpha values based on theme brightness
    final int startAlpha = isDarkMode ? 60 : 30; // Higher alpha for dark mode
    final int endAlpha = isDarkMode ? 120 : 60; // Higher alpha for dark mode

    final Color gradientStart = theme.colorScheme.primary.withAlpha(startAlpha);
    final Color gradientEnd = theme.colorScheme.primary.withAlpha(endAlpha);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: Container(
        decoration: showGradient
            ? BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [gradientStart, gradientEnd],
                ),
              )
            : null,
        child: child,
      ),
    );
  }

  /// Get the app bar color based on the theme
  static Color getAppBarColor(BuildContext context) {
    final theme = Theme.of(context);
    final bool isDarkMode = theme.brightness == Brightness.dark;

    // Use different alpha values based on theme brightness
    final int alpha = isDarkMode ? 150 : 120;
    return theme.colorScheme.primary.withAlpha(alpha);
  }
}
