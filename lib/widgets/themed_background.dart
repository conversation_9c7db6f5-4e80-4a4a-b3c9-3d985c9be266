import 'package:flutter/material.dart';

/// A widget that provides a background gradient based on the app's primary color.
/// This widget should be used as a parent for screens that need consistent theming.
class ThemedBackground extends StatelessWidget {
  final Widget child;

  const ThemedBackground({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bool isDarkMode = theme.brightness == Brightness.dark;

    // Use different alpha values based on theme brightness
    final Color gradientStart = isDarkMode
        ? Colors.grey[900]!.withAlpha(255)
        : theme.colorScheme.primary.withAlpha(30);
    final Color gradientEnd = isDarkMode
        ? Colors.black87.withAlpha(255)
        : theme.colorScheme.primary.withAlpha(60);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [gradientStart, gradientEnd],
          ),
        ),
        child: child,
      ),
    );
  }

  /// Get the app bar color based on the theme
  static Color getAppBarColor(BuildContext context, {bool isScrolled = false}) {
    final theme = Theme.of(context);
    final bool isDarkMode = theme.brightness == Brightness.dark;

    // Use different alpha values based on theme brightness and scroll state
    // Higher alpha when scrolled for better visibility
    final int alpha = isScrolled
        ? (isDarkMode ? 200 : 180)  // More opaque when scrolled
        : (isDarkMode ? 150 : 120); // Less opaque when not scrolled
    return theme.colorScheme.primary.withAlpha(alpha);
  }
}
