import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:ailacarte/theme/custom_theme.dart';

class SettingItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final Color? iconColor;
  final bool showDivider;

  const SettingItem({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.iconColor,
    this.showDivider = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: CustomTheme.getShadowColor(context, opacity: 0.05),
              blurRadius: 8,
              spreadRadius: 0,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
            child: Container(
              decoration: BoxDecoration(
                color: theme.cardColor.withAlpha(200),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isDarkMode
                      ? Colors.white.withAlpha(20)
                      : Colors.black.withAlpha(10),
                  width: 0.5,
                ),
              ),
              child: ListTile(
                leading: Icon(
                  icon,
                  color: (iconColor ?? theme.colorScheme.primary).withAlpha(220),
                ),
                title: Text(
                  title,
                  style: TextStyle(
                    color: CustomTheme.getPrimaryTextColor(context).withAlpha(220),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                subtitle: subtitle != null
                    ? Text(
                        subtitle!,
                        style: TextStyle(
                          color: CustomTheme.getSecondaryTextColor(context).withAlpha(180),
                          fontSize: 13,
                        ),
                      )
                    : null,
                trailing: trailing,
                onTap: onTap,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
