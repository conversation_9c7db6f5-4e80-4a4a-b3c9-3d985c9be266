import 'dart:ui';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:ailacarte/models/grocery_enums.dart';
import 'package:flutter_gen/gen_l10n/l10n.dart';
import 'package:ailacarte/database/database.dart';

class GroceryItemDialog extends StatefulWidget {
  final GroceryItem? item; // null for add, non-null for edit
  final String? recipeId; // for adding items from recipe
  final Function(String name, String quantity, String category, String? notes, String? recipeId) onSave;

  const GroceryItemDialog({
    super.key,
    this.item,
    this.recipeId,
    required this.onSave,
  });

  @override
  State<GroceryItemDialog> createState() => _GroceryItemDialogState();
}

class _GroceryItemDialogState extends State<GroceryItemDialog> {
  final _nameController = TextEditingController();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  GroceryCategory _selectedCategory = GroceryCategory.other;
  GroceryUnit _selectedUnit = GroceryUnit.pieces;

  bool get _isEditing => widget.item != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      final item = widget.item!;
      _nameController.text = item.name;
      _selectedCategory = GroceryCategory.fromString(item.category);
      _notesController.text = item.notes ?? '';
      final quantityParts = item.quantity.split(' ');
      if (quantityParts.length >= 2) {
        _amountController.text = quantityParts[0];
        final unitString = quantityParts.sublist(1).join(' ');
        _selectedUnit = GroceryUnit.fromString(unitString);
      } else {
        _amountController.text = item.quantity;
        _selectedUnit = GroceryUnit.pieces;
      }
    } else {
      _amountController.text = '1';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = CupertinoTheme.of(context);

    return CupertinoAlertDialog(
      title: Text(_isEditing ? l10n.editGroceryItem : l10n.addGroceryItem),
      content: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 30, sigmaY: 30),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 8),
              CupertinoTextField(
                controller: _nameController,
                placeholder: l10n.itemName,
                autofocus: !_isEditing,
                decoration: BoxDecoration(
                  color: CupertinoColors.systemBackground.withAlpha(180),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: CupertinoColors.separator.withAlpha(50),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: CupertinoTextField(
                      controller: _amountController,
                      placeholder: l10n.amount,
                      keyboardType: TextInputType.number,
                      decoration: BoxDecoration(
                        color: CupertinoColors.systemBackground.withAlpha(180),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: CupertinoColors.separator.withAlpha(50),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    flex: 1,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: CupertinoColors.systemBackground.withAlpha(180),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: CupertinoColors.separator.withAlpha(50),
                        ),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<GroceryUnit>(
                          value: _selectedUnit,
                          isExpanded: true,
                          icon: Icon(
                            CupertinoIcons.chevron_down,
                            color: CupertinoColors.inactiveGray,
                          ),
                          style: TextStyle(
                            color: CupertinoColors.label,
                            fontSize: 16,
                          ),
                          dropdownColor: CupertinoColors.systemBackground,
                          items: GroceryUnit.values.map((unit) {
                            return DropdownMenuItem<GroceryUnit>(
                              value: unit,
                              child: Text(_getLocalizedUnit(l10n, unit)),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _selectedUnit = value;
                              });
                            }
                          },
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: CupertinoColors.systemBackground.withAlpha(180),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: CupertinoColors.separator.withAlpha(50),
                  ),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<GroceryCategory>(
                    value: _selectedCategory,
                    isExpanded: true,
                    hint: Text(l10n.category),
                    icon: Icon(
                      CupertinoIcons.chevron_down,
                      color: CupertinoColors.inactiveGray,
                    ),
                    style: TextStyle(
                      color: CupertinoColors.label,
                      fontSize: 16,
                    ),
                    dropdownColor: CupertinoColors.systemBackground,
                    items: GroceryCategory.values.map((category) {
                      return DropdownMenuItem<GroceryCategory>(
                        value: category,
                        child: Text(_getLocalizedCategory(l10n, category)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedCategory = value;
                        });
                      }
                    },
                  ),
                ),
              ),
              const SizedBox(height: 12),
              CupertinoTextField(
                controller: _notesController,
                placeholder: l10n.notesOptional,
                maxLines: 2,
                decoration: BoxDecoration(
                  color: CupertinoColors.systemBackground.withAlpha(180),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: CupertinoColors.separator.withAlpha(50),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        CupertinoDialogAction(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(l10n.cancel),
        ),
        CupertinoDialogAction(
          onPressed: _handleSave,
          isDefaultAction: true,
          child: Text(_isEditing ? l10n.save : l10n.add),
        ),
      ],
    );
  }

  void _handleSave() {
    if (_nameController.text.trim().isNotEmpty) {
      final amount = _amountController.text.trim().isNotEmpty 
          ? _amountController.text.trim() 
          : '1';
      final quantity = '$amount ${_selectedUnit.value}';
      widget.onSave(
        _nameController.text.trim(),
        quantity,
        _selectedCategory.value,
        _notesController.text.trim().isNotEmpty 
            ? _notesController.text.trim() 
            : null,
        widget.recipeId,
      );
      Navigator.of(context).pop();
    }
  }

  String _getLocalizedCategory(AppLocalizations l10n, GroceryCategory category) {
    switch (category) {
      case GroceryCategory.vegetables:
        return l10n.categoryVegetables;
      case GroceryCategory.fruits:
        return l10n.categoryFruits;
      case GroceryCategory.meat:
        return l10n.categoryMeat;
      case GroceryCategory.dairy:
        return l10n.categoryDairy;
      case GroceryCategory.pantry:
        return l10n.categoryPantry;
      case GroceryCategory.bakery:
        return l10n.categoryBakery;
      case GroceryCategory.frozen:
        return l10n.categoryFrozen;
      case GroceryCategory.beverages:
        return l10n.categoryBeverages;
      case GroceryCategory.snacks:
        return l10n.categorySnacks;
      case GroceryCategory.spices:
        return l10n.categorySpices;
      case GroceryCategory.other:
        return l10n.categoryOther;
    }
  }

  String _getLocalizedUnit(AppLocalizations l10n, GroceryUnit unit) {
    switch (unit) {
      case GroceryUnit.pieces:
        return l10n.unitPieces;
      case GroceryUnit.pounds:
        return l10n.unitPounds;
      case GroceryUnit.ounces:
        return l10n.unitOunces;
      case GroceryUnit.cups:
        return l10n.unitCups;
      case GroceryUnit.tablespoons:
        return l10n.unitTablespoons;
      case GroceryUnit.teaspoons:
        return l10n.unitTeaspoons;
      case GroceryUnit.liters:
        return l10n.unitLiters;
      case GroceryUnit.milliliters:
        return l10n.unitMilliliters;
      case GroceryUnit.kilograms:
        return l10n.unitKilograms;
      case GroceryUnit.grams:
        return l10n.unitGrams;
      case GroceryUnit.dozen:
        return l10n.unitDozen;
      case GroceryUnit.bunch:
        return l10n.unitBunch;
      case GroceryUnit.bag:
        return l10n.unitBag;
      case GroceryUnit.box:
        return l10n.unitBox;
      case GroceryUnit.can:
        return l10n.unitCan;
      case GroceryUnit.bottle:
        return l10n.unitBottle;
    }
  }
}
