import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:ailacarte/widgets/translucent_dialog.dart';
import 'package:ailacarte/core/config/app_config.dart';
import 'package:flutter_gen/gen_l10n/l10n.dart';

class AddGroceryItemDialog extends StatefulWidget {
  final Function(String name, String quantity, String category, String? notes) onAdd;

  const AddGroceryItemDialog({
    super.key,
    required this.onAdd,
  });

  @override
  State<AddGroceryItemDialog> createState() => _AddGroceryItemDialogState();
}

class _AddGroceryItemDialogState extends State<AddGroceryItemDialog> {
  final _nameController = TextEditingController();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  String _selectedCategory = AppConfig.groceryCategories.first;
  String _selectedUnit = 'pieces';

  // Common grocery units
  final List<String> _units = [
    'pieces', 'lbs', 'oz', 'cups', 'tbsp', 'tsp', 'liters', 'ml',
    'kg', 'g', 'dozen', 'bunch', 'bag', 'box', 'can', 'bottle'
  ];

  @override
  void initState() {
    super.initState();
    _amountController.text = '1';
  }

  @override
  void dispose() {
    _nameController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return TranslucentDialog(
      title: l10n.addGroceryItem,
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Item Name
            CupertinoTextField(
              controller: _nameController,
              placeholder: l10n.itemName,
              autofocus: true,
              decoration: BoxDecoration(
                color: theme.cardColor.withAlpha(100),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.outline.withAlpha(50),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Amount and Unit Row
            Row(
              children: [
                // Amount
                Expanded(
                  flex: 1,
                  child: CupertinoTextField(
                    controller: _amountController,
                    placeholder: l10n.amount,
                    keyboardType: TextInputType.number,
                    decoration: BoxDecoration(
                      color: theme.cardColor.withAlpha(100),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: theme.colorScheme.outline.withAlpha(50),
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // Unit Dropdown
                Expanded(
                  flex: 1,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: theme.cardColor.withAlpha(100),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: theme.colorScheme.outline.withAlpha(50),
                      ),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: _selectedUnit,
                        isExpanded: true,
                        icon: Icon(
                          Icons.arrow_drop_down,
                          color: theme.colorScheme.onSurface.withAlpha(150),
                        ),
                        style: TextStyle(
                          color: theme.colorScheme.onSurface,
                          fontSize: 16,
                        ),
                        dropdownColor: theme.cardColor,
                        items: _units.map((unit) {
                          return DropdownMenuItem<String>(
                            value: unit,
                            child: Text(_getLocalizedUnit(l10n, unit)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedUnit = value;
                            });
                          }
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Category Dropdown
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: theme.cardColor.withAlpha(100),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.outline.withAlpha(50),
                ),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: _selectedCategory,
                  isExpanded: true,
                  hint: Text(l10n.category),
                  icon: Icon(
                    Icons.arrow_drop_down,
                    color: theme.colorScheme.onSurface.withAlpha(150),
                  ),
                  style: TextStyle(
                    color: theme.colorScheme.onSurface,
                    fontSize: 16,
                  ),
                  dropdownColor: theme.cardColor,
                  items: AppConfig.groceryCategories.map((category) {
                    return DropdownMenuItem<String>(
                      value: category,
                      child: Text(_getLocalizedCategory(l10n, category)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedCategory = value;
                      });
                    }
                  },
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Notes (Optional)
            CupertinoTextField(
              controller: _notesController,
              placeholder: l10n.notesOptional,
              maxLines: 2,
              decoration: BoxDecoration(
                color: theme.cardColor.withAlpha(100),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.outline.withAlpha(50),
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        CupertinoButton(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            l10n.cancel,
            style: TextStyle(color: theme.colorScheme.onSurface.withAlpha(150)),
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        CupertinoButton(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            l10n.add,
            style: TextStyle(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          onPressed: () {
            if (_nameController.text.trim().isNotEmpty) {
              // Combine amount and unit into quantity string
              final amount = _amountController.text.trim().isNotEmpty
                  ? _amountController.text.trim()
                  : '1';
              final quantity = '$amount $_selectedUnit';

              widget.onAdd(
                _nameController.text.trim(),
                quantity,
                _selectedCategory,
                _notesController.text.trim().isNotEmpty
                    ? _notesController.text.trim()
                    : null,
              );
              Navigator.of(context).pop();
            }
          },
        ),
      ],
    );
  }

  String _getLocalizedCategory(AppLocalizations l10n, String category) {
    switch (category) {
      case 'Vegetables':
        return l10n.categoryVegetables;
      case 'Fruits':
        return l10n.categoryFruits;
      case 'Meat':
        return l10n.categoryMeat;
      case 'Dairy':
        return l10n.categoryDairy;
      case 'Pantry':
        return l10n.categoryPantry;
      case 'Bakery':
        return l10n.categoryBakery;
      case 'Frozen':
        return l10n.categoryFrozen;
      case 'Beverages':
        return l10n.categoryBeverages;
      case 'Snacks':
        return l10n.categorySnacks;
      case 'Spices':
        return l10n.categorySpices;
      case 'Other':
        return l10n.categoryOther;
      default:
        return category;
    }
  }

  String _getLocalizedUnit(AppLocalizations l10n, String unit) {
    // For now, return the unit as-is since these are commonly understood
    // In the future, you could add localized unit names if needed
    return unit;
  }
}
