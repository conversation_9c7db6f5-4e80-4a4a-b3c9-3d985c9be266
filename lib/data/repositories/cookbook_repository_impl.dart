import 'package:ailacarte/database/database.dart';
import 'package:ailacarte/domain/repositories/cookbook_repository.dart';
import 'package:ailacarte/models/cookbook.dart' as cookbook_model;
import 'package:drift/drift.dart';
import 'package:uuid/uuid.dart';

class CookbookRepositoryImpl implements CookbookRepository {
  final AppDatabase _database;
  final Uuid _uuid = const Uuid();

  CookbookRepositoryImpl(this._database);

  @override
  Future<List<cookbook_model.Cookbook>> getCookbooks() async {
    try {
      final cookbookEntities = await _database.getAllCookbooks();
      return cookbookEntities.map(_mapToModel).toList();
    } catch (e) {
      throw Exception('Failed to get cookbooks: $e');
    }
  }

  @override
  Future<cookbook_model.Cookbook?> getCookbookById(String id) async {
    try {
      final cookbookEntity = await _database.getCookbookById(id);
      return cookbookEntity != null ? _mapToModel(cookbookEntity) : null;
    } catch (e) {
      throw Exception('Failed to get cookbook by id: $e');
    }
  }

  @override
  Future<cookbook_model.Cookbook?> getDefaultCookbook() async {
    try {
      final cookbookEntity = await _database.getDefaultCookbook();
      return cookbookEntity != null ? _mapToModel(cookbookEntity) : null;
    } catch (e) {
      throw Exception('Failed to get default cookbook: $e');
    }
  }

  @override
  Future<cookbook_model.Cookbook> createCookbook(cookbook_model.Cookbook cookbook) async {
    try {
      final id = _uuid.v4();
      final now = DateTime.now();
      
      final companion = CookbooksCompanion.insert(
        id: id,
        name: cookbook.name,
        description: cookbook.description,
        imageUrl: Value(cookbook.imageUrl),
        userId: Value(cookbook.userId),
        color: Value(cookbook.color),
        createdAt: now,
        updatedAt: now,
        isDefault: Value(cookbook.isDefault),
      );

      await _database.insertCookbook(companion);
      
      return cookbook.copyWith(
        id: id,
        createdAt: now,
        updatedAt: now,
      );
    } catch (e) {
      throw Exception('Failed to create cookbook: $e');
    }
  }

  @override
  Future<cookbook_model.Cookbook> updateCookbook(cookbook_model.Cookbook cookbook) async {
    try {
      final now = DateTime.now();

      final companion = CookbooksCompanion(
        id: Value(cookbook.id),
        name: Value(cookbook.name),
        description: Value(cookbook.description),
        imageUrl: Value(cookbook.imageUrl),
        userId: Value(cookbook.userId),
        color: Value(cookbook.color),
        updatedAt: Value(now),
        isDefault: Value(cookbook.isDefault),
      );

      await _database.updateCookbook(companion);

      return cookbook.copyWith(updatedAt: now);
    } catch (e) {
      throw Exception('Failed to update cookbook: $e');
    }
  }

  @override
  Future<void> deleteCookbook(String id) async {
    try {
      await _database.deleteCookbook(id);
    } catch (e) {
      throw Exception('Failed to delete cookbook: $e');
    }
  }

  @override
  Future<void> syncCookbooks() async {
    // TODO: Implement sync with Supabase
    // This would sync local cookbooks with the remote database
  }

  cookbook_model.Cookbook _mapToModel(Cookbook entity) {
    return cookbook_model.Cookbook(
      id: entity.id,
      name: entity.name,
      description: entity.description,
      imageUrl: entity.imageUrl,
      userId: entity.userId,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      isDefault: entity.isDefault,
    );
  }
}
