import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsPreferences {
  // Keys
  static const String _onboardingCompletedKey = 'onboarding_completed';
  static const String _themeModeKey = 'theme_mode';
  static const String _languageKey = 'language';
  static const String _firstLaunchKey = 'first_launch';
  static const String _paywallShownKey = 'paywall_shown';

  // Notification settings
  static const String _notificationPrefix = 'notification_';
  static const String _enabledSuffix = '_enabled';
  static const String _timeSuffix = '_time';
  static const String _frequencySuffix = '_frequency';
  static const String _daysSuffix = '_days';

  // Notification types
  static const String notificationTypeRecipe = 'recipe';
  static const String notificationTypeMealPlan = 'meal_plan';
  static const String notificationTypeGrocery = 'grocery';

  // Frequency types
  static const String frequencyDaily = 'daily';
  static const String frequencyWeekdays = 'weekdays';
  static const String frequencyWeekends = 'weekends';
  static const String frequencyCustom = 'custom';

  // Default values
  static const String defaultTime = '09:00';
  static const String defaultFrequency = frequencyDaily;
  static const List<bool> defaultDays = [true, true, true, true, true, false, false];

  // Get all notification types
  static List<String> get notificationTypes => [
    notificationTypeRecipe,
    notificationTypeMealPlan,
    notificationTypeGrocery,
  ];

  // Get display name for notification type
  static String getNotificationTypeName(String type, BuildContext context) {
    return _getDefaultNotificationTypeName(type);
  }

  // Get default notification type name
  static String _getDefaultNotificationTypeName(String type) {
    switch (type) {
      case notificationTypeRecipe:
        return 'Recipe Reminders';
      case notificationTypeMealPlan:
        return 'Meal Planning';
      case notificationTypeGrocery:
        return 'Grocery Lists';
      default:
        return type;
    }
  }

  // Onboarding
  static Future<bool> getOnboardingCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_onboardingCompletedKey) ?? false;
  }

  static Future<void> setOnboardingCompleted(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_onboardingCompletedKey, value);
  }

  // Theme
  static Future<ThemeMode> getThemeMode() async {
    final prefs = await SharedPreferences.getInstance();
    final themeIndex = prefs.getInt(_themeModeKey) ?? 0;
    return ThemeMode.values[themeIndex];
  }

  static Future<void> setThemeMode(ThemeMode themeMode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_themeModeKey, themeMode.index);
  }

  // Language
  static Future<String> getLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_languageKey) ?? 'en';
  }

  static Future<void> setLanguage(String languageCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_languageKey, languageCode);
  }

  // First launch
  static Future<bool> getFirstLaunch() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_firstLaunchKey) ?? true;
  }

  static Future<void> setFirstLaunch(bool isFirst) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_firstLaunchKey, isFirst);
  }

  // Paywall shown flag
  static Future<bool> getPaywallShown() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_paywallShownKey) ?? false;
  }

  static Future<void> setPaywallShown(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_paywallShownKey, value);
  }

  // General notifications enabled
  static const String _notificationsEnabledKey = 'notifications_enabled';

  static Future<bool> getNotificationsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_notificationsEnabledKey) ?? false;
  }

  static Future<void> setNotificationsEnabled(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_notificationsEnabledKey, value);
  }

  // Notification settings by type
  static Future<bool> getNotificationEnabled(String type) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('$_notificationPrefix$type$_enabledSuffix') ?? false;
  }

  static Future<void> setNotificationEnabled(String type, bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('$_notificationPrefix$type$_enabledSuffix', value);
  }

  static Future<String> getNotificationTime(String type) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('$_notificationPrefix$type$_timeSuffix') ?? defaultTime;
  }

  static Future<void> setNotificationTime(String type, String time) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('$_notificationPrefix$type$_timeSuffix', time);
  }

  static Future<String> getNotificationFrequency(String type) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('$_notificationPrefix$type$_frequencySuffix') ?? defaultFrequency;
  }

  static Future<void> setNotificationFrequency(String type, String frequency) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('$_notificationPrefix$type$_frequencySuffix', frequency);
  }

  static Future<List<bool>> getNotificationDays(String type) async {
    final prefs = await SharedPreferences.getInstance();
    final daysString = prefs.getString('$_notificationPrefix$type$_daysSuffix');
    if (daysString == null) return List<bool>.from(defaultDays);
    return daysString.split(',').map((e) => e == '1').toList();
  }

  static Future<void> setNotificationDays(String type, List<bool> days) async {
    final prefs = await SharedPreferences.getInstance();
    final daysString = days.map((e) => e ? '1' : '0').join(',');
    await prefs.setString('$_notificationPrefix$type$_daysSuffix', daysString);
  }

  // Clear all notification settings
  static Future<void> clearAllNotifications() async {
    final prefs = await SharedPreferences.getInstance();
    final keys = prefs.getKeys();
    for (final key in keys) {
      if (key.startsWith(_notificationPrefix)) {
        await prefs.remove(key);
      }
    }
  }

  // Clear all settings
  static Future<void> clearAll() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }
}
