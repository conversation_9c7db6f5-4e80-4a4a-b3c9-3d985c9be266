import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ailacarte/pages/onboarding/onboarding_screen.dart';
import 'package:ailacarte/pages/recipe_list_screen.dart';
import 'package:ailacarte/pages/grocery_list_screen.dart';
import 'package:ailacarte/pages/settings_screen.dart';
import 'package:ailacarte/widgets/responsive_navigation_layout.dart';
import 'package:ailacarte/services/auth_service.dart';
import 'package:ailacarte/pages/auth/login_register_page.dart';
import 'package:ailacarte/pages/gatekeeper_screen.dart';
import 'package:ailacarte/pages/paywall_screen.dart';
import 'package:ailacarte/pages/add_cookbook_screen.dart';
import 'package:ailacarte/di/service_locator.dart';

class AppRouter {
  static final GlobalKey<NavigatorState> rootNavigatorKey = GlobalKey<NavigatorState>();
  
  static final GoRouter router = GoRouter(
    navigatorKey: rootNavigatorKey,
    initialLocation: '/gatekeeper',
    debugLogDiagnostics: true,
    // Refresh on auth state changes
    refreshListenable: _AuthStateNotifier(),
    routes: [
      // Gatekeeper route (determines initial flow)
      GoRoute(
        path: '/gatekeeper',
        pageBuilder: (context, state) => const MaterialPage(
          child: GatekeeperScreen(),
        ),
      ),
      
      // Onboarding route
      GoRoute(
        path: '/onboarding',
        pageBuilder: (context, state) => MaterialPage(
          child: OnboardingScreen(onComplete: () {}),
        ),
      ),
      
      // Paywall route
      GoRoute(
        path: '/paywall',
        pageBuilder: (context, state) => MaterialPage(
          child: PaywallScreen(onComplete: () {}),
        ),
      ),
      
      // Login route
      GoRoute(
        path: '/login',
        pageBuilder: (context, state) => MaterialPage(
          child: LoginRegisterPage(onLoginSuccess: () {}),
        ),
      ),

      // Add cookbook route
      GoRoute(
        path: '/add-cookbook',
        pageBuilder: (context, state) => const MaterialPage(
          child: AddCookbookScreen(),
        ),
      ),

      // Protected routes (require authentication)
      ShellRoute(
        builder: (context, state, child) {
          return ResponsiveNavigationLayout(
            currentIndex: _getCurrentIndex(state.uri.path),
            onTap: (index) => _onItemTapped(index, context),
            children: const [
              RecipeListScreen(),
              GroceryListScreen(),
              SettingsScreen(),
            ],
          );
        },
        routes: [
          GoRoute(
            path: '/',
            pageBuilder: (context, state) => const NoTransitionPage(
              child: RecipeListScreen(),
            ),
          ),
          GoRoute(
            path: '/grocery',
            pageBuilder: (context, state) => const NoTransitionPage(
              child: GroceryListScreen(),
            ),
          ),
          GoRoute(
            path: '/settings',
            pageBuilder: (context, state) => const NoTransitionPage(
              child: SettingsScreen(),
            ),
          ),
        ],
      ),
    ],
  );

  static int _getCurrentIndex(String path) {
    switch (path) {
      case '/':
        return 0;
      case '/grocery':
        return 1;
      case '/settings':
        return 2;
      default:
        return 0;
    }
  }

  static void _onItemTapped(int index, BuildContext context) {
    switch (index) {
      case 0:
        context.go('/');
        break;
      case 1:
        context.go('/grocery');
        break;
      case 2:
        context.go('/settings');
        break;
    }
  }
}

class _AuthStateNotifier extends ChangeNotifier {
  _AuthStateNotifier() {
    final authService = getIt<AuthService>();
    authService.authStateChanges.listen((_) {
      notifyListeners();
    });
  }
}
