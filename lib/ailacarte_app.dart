import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/l10n.dart';
import 'package:ailacarte/theme/theme.dart';
import 'package:ailacarte/blocs/theme_bloc/theme_bloc.dart';
import 'package:ailacarte/blocs/language_bloc/language_bloc.dart';
import 'package:ailacarte/blocs/cookbook_bloc/cookbook_bloc.dart';
import 'package:ailacarte/blocs/cookbook_bloc/cookbook_event.dart';
import 'package:ailacarte/di/service_locator.dart';
import 'package:ailacarte/router/app_router.dart';
import 'package:ailacarte/services/analytics_service.dart';
import 'package:flutter/services.dart';

class AILaCarteApp extends StatefulWidget {
  const AILaCarteApp({super.key});

  @override
  State<AILaCarteApp> createState() => _AILaCarteAppState();
}

class _AILaCarteAppState extends State<AILaCarteApp> {
  @override
  void initState() {
    super.initState();
    
    // Initialize app lifecycle tracking
    _setupAppLifecycleTracking();
  }
  
  void _setupAppLifecycleTracking() {
    // Track app open
    AnalyticsService.instance.logAppOpen();
    
    // Track app lifecycle changes
    SystemChannels.lifecycle.setMessageHandler((msg) async {
      debugPrint('SystemChannels> $msg');
      if (msg != null) {
        if (msg == AppLifecycleState.paused.toString()) {
          AnalyticsService.instance.logAppBackground();
        } else if (msg == AppLifecycleState.resumed.toString()) {
          AnalyticsService.instance.logAppOpen();
        }
      }
      return null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => ThemeBloc()),
        BlocProvider(create: (_) => LanguageBloc()),
        BlocProvider(
          create: (_) => getIt<CookbookBloc>()..add(LoadCookbooksEvent()),
        ),
      ],
      child: Builder(
        builder: (context) {
          return BlocBuilder<ThemeBloc, ThemeState>(
            builder: (context, themeState) {
              return BlocBuilder<LanguageBloc, LanguageState>(
                builder: (context, languageState) {
                  return MaterialApp.router(
                    title: 'AI La Carte',
                    debugShowCheckedModeBanner: false,
                    theme: AppTheme.lightTheme,
                    darkTheme: AppTheme.darkTheme,
                    themeMode: themeState.themeMode,
                    routeInformationProvider: AppRouter.router.routeInformationProvider,
                    routeInformationParser: AppRouter.router.routeInformationParser,
                    routerDelegate: AppRouter.router.routerDelegate,
                    backButtonDispatcher: AppRouter.router.backButtonDispatcher,
                    localizationsDelegates: AppLocalizations.localizationsDelegates,
                    locale: languageState.locale,
                    supportedLocales: AppLocalizations.supportedLocales,
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}
