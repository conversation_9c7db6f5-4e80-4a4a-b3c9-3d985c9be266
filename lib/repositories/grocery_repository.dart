import 'package:ailacarte/database/database.dart';

abstract class GroceryRepository {
  Future<List<GroceryItem>> getAllGroceryItems();
  Future<List<Recipe>> getRecipesWithGroceryItems();
  Future<void> insertGroceryItem(GroceryItemsCompanion item);
  Future<void> updateGroceryItem(GroceryItemsCompanion item);
  Future<void> deleteGroceryItem(String id);
}

class GroceryRepositoryImpl implements GroceryRepository {
  final AppDatabase _database;

  GroceryRepositoryImpl({required AppDatabase database}) : _database = database;

  @override
  Future<List<GroceryItem>> getAllGroceryItems() async {
    return await _database.getAllGroceryItems();
  }

  @override
  Future<List<Recipe>> getRecipesWithGroceryItems() async {
    return await _database.getRecipesWithGroceryItems();
  }

  @override
  Future<void> insertGroceryItem(GroceryItemsCompanion item) async {
    await _database.insertGroceryItem(item);
  }

  @override
  Future<void> updateGroceryItem(GroceryItemsCompanion item) async {
    await _database.updateGroceryItem(item);
  }

  @override
  Future<void> deleteGroceryItem(String id) async {
    await _database.deleteGroceryItem(id);
  }
}
